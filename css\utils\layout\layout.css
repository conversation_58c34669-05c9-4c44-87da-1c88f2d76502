:root {

  /* Grid variables */
  --mico-grid-columns: 12;
  --mico-grid-gap: 1rem;
  --mico-min-column-width: 200px;
}

/* Box Model */
.box-border { box-sizing: border-box; }
.box-content { box-sizing: content-box; }

.decoration-slice { box-decoration-break: slice; }
.decoration-clone { box-decoration-break: clone; }

/* Float and Clear */
.float-right { float: right !important; }
.float-left { float: left !important; }
.float-none { float: none !important; }

.clear-left { clear: left !important; }
.clear-right { clear: right !important; }
.clear-both { clear: both !important; }
.clear-none { clear: none !important; }

/* Isolation */
.isolate { isolation: isolate; }
.isolate-auto { isolation: auto; }

/* Object Fit and Position */
.object-contain { object-fit: contain; }
.object-cover { object-fit: cover; }
.object-fill { object-fit: fill; }
.object-none { object-fit: none; }
.object-scale-down { object-fit: scale-down; }

.object-top { object-position: top; }
.object-bottom { object-position: bottom; }
.object-center { object-position: center; }
.object-left { object-position: left; }
.object-right { object-position: right; }
.object-top-left { object-position: top left; }
.object-top-right { object-position: top right; }
.object-bottom-left { object-position: bottom left; }
.object-bottom-right { object-position: bottom right; }

/* Overflow */
.overflow-visible { overflow: visible; }
.overflow-hidden { overflow: hidden; }
.overflow-clip { overflow: clip; }
.overflow-scroll { overflow: scroll; }
.overflow-auto { overflow: auto; }

.overflow-x-visible { overflow-x: visible; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-x-clip { overflow-x: clip; }
.overflow-x-scroll { overflow-x: scroll; }
.overflow-x-auto { overflow-x: auto; }

.overflow-y-visible { overflow-y: visible; }
.overflow-y-hidden { overflow-y: hidden; }
.overflow-y-clip { overflow-y: clip; }
.overflow-y-scroll { overflow-y: scroll; }
.overflow-y-auto { overflow-y: auto; }

/* Visibility */
.visibility-visible { visibility: visible; }
.visibility-hidden { visibility: hidden; }
.visibility-collapse { visibility: collapse; }

/* Overscroll Behavior */
.overscroll-auto { overscroll-behavior: auto; }
.overscroll-contain { overscroll-behavior: contain; }
.overscroll-none { overscroll-behavior: none; }

.overscroll-x-auto { overscroll-behavior-x: auto; }
.overscroll-x-contain { overscroll-behavior-x: contain; }
.overscroll-x-none { overscroll-behavior-x: none; }

.overscroll-y-auto { overscroll-behavior-y: auto; }
.overscroll-y-contain { overscroll-behavior-y: contain; }
.overscroll-y-none { overscroll-behavior-y: none; }

/* Position */
.static { position: static; }
.fixed { position: fixed; }
.absolute { position: absolute; }
.relative { position: relative; }
.sticky { position: sticky; }

.top-0 { top: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }

.inset-0 { inset: 0; }
.inset-auto { inset: auto; }
.inset-x-0 { left: 0; right: 0; }
.inset-y-0 { top: 0; bottom: 0; }
.inset-x-auto { left: auto; right: auto; }
.inset-y-auto { top: auto; bottom: auto; }

/* Display */
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }
.d-table { display: table !important; }
.d-table-row { display: table-row !important; }
.d-table-cell { display: table-cell !important; }
.d-table-column { display: table-column !important; }
.d-table-column-group { display: table-column-group !important; }
.d-table-header-group { display: table-header-group !important; }
.d-table-footer-group { display: table-footer-group !important; }
.d-table-row-group { display: table-row-group !important; }
.d-flow-root { display: flow-root !important; }
.d-contents { display: contents !important; }
.d-list-item { display: list-item !important; }
.d-ruby { display: ruby !important; }
.d-ruby-base { display: ruby-base !important; }
.d-ruby-text { display: ruby-text !important; }
.d-ruby-base-container { display: ruby-base-container !important; }

/* Flexbox */
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-row-reverse { flex-direction: row-reverse !important; }
.flex-column-reverse { flex-direction: column-reverse !important; }


.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.flex-wrap-reverse { flex-wrap: wrap-reverse !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.justify-content-evenly { justify-content: space-evenly !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

.align-content-start { align-content: flex-start !important; }
.align-content-end { align-content: flex-end !important; }
.align-content-center { align-content: center !important; }
.align-content-between { align-content: space-between !important; }
.align-content-around { align-content: space-around !important; }
.align-content-stretch { align-content: stretch !important; }

.align-self-auto { align-self: auto !important; }
.align-self-start { align-self: flex-start !important; }
.align-self-end { align-self: flex-end !important; }
.align-self-center { align-self: center !important; }
.align-self-baseline { align-self: baseline !important; }
.align-self-stretch { align-self: stretch !important; }

.flex-grow-0 { flex-grow: 0 !important; }
.flex-grow-1 { flex-grow: 1 !important; }
.flex-shrink-0 { flex-shrink: 0 !important; }
.flex-shrink-1 { flex-shrink: 1 !important; }

.flex-1 { flex: 1 1 0% !important; }
.flex-auto { flex: 1 1 auto !important; }
.flex-initial { flex: 0 1 auto !important; }
.flex-none { flex: none !important; }

/* Z-index */
.z-auto { z-index: auto; }
.z-0 { z-index: 0; }
.z-4 { z-index: 4; }
.z-8 { z-index: 8; }
.z-12 { z-index: 12; }
.z-16 { z-index: 16; }
.z-20 { z-index: 20; }
.z-n4 { z-index: -4; }
.z-n8 { z-index: -8; }
.z-n12 { z-index: -12; }
.z-n16 { z-index: -16; }
.z-n20 { z-index: -20; }
.z-100 { z-index: 100; }
.z-200 { z-index: 200; }
.z-max { z-index: 9999; }

/* Gap */
.gap-0 { gap: var(--mico-size-0) !important; }
.gap-4 { gap: var(--mico-size-4) !important; }
.gap-8 { gap: var(--mico-size-8) !important; }
.gap-12 { gap: var(--mico-size-12) !important; }
.gap-16 { gap: var(--mico-size-16) !important; }
.gap-20 { gap: var(--mico-size-20) !important; }
.gap-24 { gap: var(--mico-size-24) !important; }
.gap-28 { gap: var(--mico-size-28) !important; }
.gap-32 { gap: var(--mico-size-32) !important; }
.gap-36 { gap: var(--mico-size-36) !important; }
.gap-40 { gap: var(--mico-size-40) !important; }

/* Grid */
.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
.grid-cols-12 { grid-template-columns: repeat(12, 1fr); }

.grid-rows-1 { grid-template-rows: repeat(1, minmax(0, 1fr)); }
.grid-rows-2 { grid-template-rows: repeat(2, minmax(0, 1fr)); }
.grid-rows-3 { grid-template-rows: repeat(3, minmax(0, 1fr)); }
.grid-rows-4 { grid-template-rows: repeat(4, minmax(0, 1fr)); }
.grid-rows-5 { grid-template-rows: repeat(5, minmax(0, 1fr)); }
.grid-rows-6 { grid-template-rows: repeat(6, minmax(0, 1fr)); }

.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(var(--mico-min-column-width), 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(var(--mico-min-column-width), 1fr));
}

[data-grid-area] {
  grid-area: var(--mico-grid-area);
}

[data-grid-template-areas] {
  grid-template-areas: var(--mico-grid-template-areas);
}

/* Width and Height */
.w-none { width: none !important; }
.w-auto { width: auto !important; }
.w-screen { width: 100vw !important; }
.w-10p { width: 10% !important; }
.w-20p { width: 20% !important; }
.w-30p { width: 30% !important; }
.w-40p { width: 40% !important; }
.w-50p { width: 50% !important; }
.w-60p { width: 60% !important; }
.w-70p { width: 70% !important; }
.w-80p { width: 80% !important; }
.w-90p { width: 90% !important; }
.w-100p { width: 100% !important; }

.h-none { height: none !important; }
.h-auto { height: auto !important; }
.h-screen { height: 100vh !important; }
.h-10p { height: 10% !important; }
.h-20p { height: 20% !important; }
.h-30p { height: 30% !important; }
.h-40p { height: 40% !important; }
.h-50p { height: 50% !important; }
.h-60p { height: 60% !important; }
.h-70p { height: 70% !important; }
.h-80p { height: 80% !important; }
.h-90p { height: 90% !important; }
.h-100p { height: 100% !important; }

/* Column spans */
.col-1 { grid-column: span 1; }
.col-2 { grid-column: span 2; }
.col-3 { grid-column: span 3; }
.col-4 { grid-column: span 4; }
.col-5 { grid-column: span 5; }
.col-6 { grid-column: span 6; }
.col-7 { grid-column: span 7; }
.col-8 { grid-column: span 8; }
.col-9 { grid-column: span 9; }
.col-10 { grid-column: span 10; }
.col-11 { grid-column: span 11; }
.col-12 { grid-column: span 12; }



/* Advanced grid features */
.grid-masonry {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(var(--mico-min-column-width), 1fr));
  grid-auto-rows: 1px;
}

.grid-masonry > * {
  break-inside: avoid;
}


@media (min-width: 1440px) {
  .d-3xl-none { display: none !important; }
  .d-3xl-inline { display: inline !important; }
  .d-3xl-inline-block { display: inline-block !important; }
  .d-3xl-block { display: block !important; }
  .d-3xl-flex { display: flex !important; }
  .d-3xl-inline-flex { display: inline-flex !important; }
  .d-3xl-grid { display: grid !important; }

  .grid-cols-3xl-1 { grid-template-columns: repeat(1, 1fr); }
  .grid-cols-3xl-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-cols-3xl-3 { grid-template-columns: repeat(3, 1fr); }
  .grid-cols-3xl-4 { grid-template-columns: repeat(4, 1fr); }
  .grid-cols-3xl-5 { grid-template-columns: repeat(5, 1fr); }
  .grid-cols-3xl-6 { grid-template-columns: repeat(6, 1fr); }
  .grid-cols-3xl-12 { grid-template-columns: repeat(12, 1fr); }
  
  .col-3xl-1 { grid-column: span 1; }
  .col-3xl-2 { grid-column: span 2; }
  .col-3xl-3 { grid-column: span 3; }
  .col-3xl-4 { grid-column: span 4; }
  .col-3xl-5 { grid-column: span 5; }
  .col-3xl-6 { grid-column: span 6; }
  .col-3xl-7 { grid-column: span 7; }
  .col-3xl-8 { grid-column: span 8; }
  .col-3xl-9 { grid-column: span 9; }
  .col-3xl-10 { grid-column: span 10; }
  .col-3xl-11 { grid-column: span 11; }
  .col-3xl-12 { grid-column: span 12; }


  /* Flexbox */
.flex-row-3xl { flex-direction: row !important; }
.flex-column-3xl { flex-direction: column !important; }
.flex-row-reverse-3xl { flex-direction: row-reverse !important; }
.flex-column-reverse-3xl { flex-direction: column-reverse !important; }


.flex-wrap-3xl { flex-wrap: wrap !important; }
.flex-nowrap-3xl { flex-wrap: nowrap !important; }
.flex-wrap-reverse-3xl { flex-wrap: wrap-reverse !important; }

.justify-content-start-3xl { justify-content: flex-start !important; }
.justify-content-end-3xl { justify-content: flex-end !important; }
.justify-content-center-3xl { justify-content: center !important; }
.justify-content-between-3xl { justify-content: space-between !important; }
.justify-content-around-3xl { justify-content: space-around !important; }
.justify-content-evenly-3xl { justify-content: space-evenly !important; }

.align-items-start-3xl { align-items: flex-start !important; }
.align-items-end-3xl { align-items: flex-end !important; }
.align-items-center-3xl { align-items: center !important; }
.align-items-baseline-3xl { align-items: baseline !important; }
.align-items-stretch-3xl { align-items: stretch !important; }

.align-content-start-3xl { align-content: flex-start !important; }
.align-content-end-3xl { align-content: flex-end !important; }
.align-content-center-3xl { align-content: center !important; }
.align-content-between-3xl { align-content: space-between !important; }
.align-content-around-3xl { align-content: space-around !important; }
.align-content-stretch-3xl { align-content: stretch !important; }

.align-self-auto-3xl { align-self: auto !important; }
.align-self-start-3xl { align-self: flex-start !important; }
.align-self-end-3xl { align-self: flex-end !important; }
.align-self-center-3xl { align-self: center !important; }
.align-self-baseline-3xl { align-self: baseline !important; }
.align-self-stretch-3xl { align-self: stretch !important; }

.flex-grow-0-3xl { flex-grow: 0 !important; }
.flex-grow-1-3xl { flex-grow: 1 !important; }
.flex-shrink-0-3xl { flex-shrink: 0 !important; }
.flex-shrink-1-3xl { flex-shrink: 1 !important; }

.flex-1-3xl { flex: 1 1 0% !important; }
.flex-auto-3xl { flex: 1 1 auto !important; }
.flex-initial-3xl { flex: 0 1 auto !important; }
.flex-none-3xl { flex: none !important; }


  /* Width and Height */
.w-3xl-none { width: none !important; }
.w-3xl-auto { width: auto !important; }
.w-3xl-screen { width: 100vw !important; }
.w-3xl-10p { width: 10% !important; }
.w-3xl-20p { width: 20% !important; }
.w-3xl-30p { width: 30% !important; }
.w-3xl-40p { width: 40% !important; }
.w-3xl-50p { width: 50% !important; }
.w-3xl-60p { width: 60% !important; }
.w-3xl-70p { width: 70% !important; }
.w-3xl-80p { width: 80% !important; }
.w-3xl-90p { width: 90% !important; }
.w-3xl-100p { width: 100% !important; }

.h-3xl-none { height: none !important; }
.h-3xl-auto { height: auto !important; }
.h-3xl-screen { height: 100vh !important; }
.h-3xl-10p { height: 10% !important; }
.h-3xl-20p { height: 20% !important; }
.h-3xl-30p { height: 30% !important; }
.h-3xl-40p { height: 40% !important; }
.h-3xl-50p { height: 50% !important; }
.h-3xl-60p { height: 60% !important; }
.h-3xl-70p { height: 70% !important; }
.h-3xl-80p { height: 80% !important; }
.h-3xl-90p { height: 90% !important; }
.h-3xl-100p { height: 100% !important; }
}

/* Responsive utilities */
@media (max-width: 1440px) {
  .d-2xl-none { display: none !important; }
  .d-2xl-inline { display: inline !important; }
  .d-2xl-inline-block { display: inline-block !important; }
  .d-2xl-block { display: block !important; }
  .d-2xl-flex { display: flex !important; }
  .d-2xl-inline-flex { display: inline-flex !important; }
  .d-2xl-grid { display: grid !important; }

  .grid-cols-2xl-1 { grid-template-columns: repeat(1, 1fr); }
  .grid-cols-2xl-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-cols-2xl-3 { grid-template-columns: repeat(3, 1fr); }
  .grid-cols-2xl-4 { grid-template-columns: repeat(4, 1fr); }
  .grid-cols-2xl-5 { grid-template-columns: repeat(5, 1fr); }
  .grid-cols-2xl-6 { grid-template-columns: repeat(6, 1fr); }
  .grid-cols-2xl-12 { grid-template-columns: repeat(12, 1fr); }
  
  .col-2xl-1 { grid-column: span 1; }
  .col-2xl-2 { grid-column: span 2; }
  .col-2xl-3 { grid-column: span 3; }
  .col-2xl-4 { grid-column: span 4; }
  .col-2xl-5 { grid-column: span 5; }
  .col-2xl-6 { grid-column: span 6; }
  .col-2xl-7 { grid-column: span 7; }
  .col-2xl-8 { grid-column: span 8; }
  .col-2xl-9 { grid-column: span 9; }
  .col-2xl-10 { grid-column: span 10; }
  .col-2xl-11 { grid-column: span 11; }
  .col-2xl-12 { grid-column: span 12; }


  /* Flexbox */
.flex-row-2xl { flex-direction: row !important; }
.flex-column-2xl { flex-direction: column !important; }
.flex-row-reverse-2xl { flex-direction: row-reverse !important; }
.flex-column-reverse-2xl { flex-direction: column-reverse !important; }


.flex-wrap-2xl { flex-wrap: wrap !important; }
.flex-nowrap-2xl { flex-wrap: nowrap !important; }
.flex-wrap-reverse-2xl { flex-wrap: wrap-reverse !important; }

.justify-content-start-2xl { justify-content: flex-start !important; }
.justify-content-end-2xl { justify-content: flex-end !important; }
.justify-content-center-2xl { justify-content: center !important; }
.justify-content-between-2xl { justify-content: space-between !important; }
.justify-content-around-2xl { justify-content: space-around !important; }
.justify-content-evenly-2xl { justify-content: space-evenly !important; }

.align-items-start-2xl { align-items: flex-start !important; }
.align-items-end-2xl { align-items: flex-end !important; }
.align-items-center-2xl { align-items: center !important; }
.align-items-baseline-2xl { align-items: baseline !important; }
.align-items-stretch-2xl { align-items: stretch !important; }

.align-content-start-2xl { align-content: flex-start !important; }
.align-content-end-2xl { align-content: flex-end !important; }
.align-content-center-2xl { align-content: center !important; }
.align-content-between-2xl { align-content: space-between !important; }
.align-content-around-2xl { align-content: space-around !important; }
.align-content-stretch-2xl { align-content: stretch !important; }

.align-self-auto-2xl { align-self: auto !important; }
.align-self-start-2xl { align-self: flex-start !important; }
.align-self-end-2xl { align-self: flex-end !important; }
.align-self-center-2xl { align-self: center !important; }
.align-self-baseline-2xl { align-self: baseline !important; }
.align-self-stretch-2xl { align-self: stretch !important; }

.flex-grow-0-2xl { flex-grow: 0 !important; }
.flex-grow-1-2xl { flex-grow: 1 !important; }
.flex-shrink-0-2xl { flex-shrink: 0 !important; }
.flex-shrink-1-2xl { flex-shrink: 1 !important; }

.flex-1-2xl { flex: 1 1 0% !important; }
.flex-auto-2xl { flex: 1 1 auto !important; }
.flex-initial-2xl { flex: 0 1 auto !important; }
.flex-none-2xl { flex: none !important; }


  /* Width and Height */
.w-2xl-none { width: none !important; }
.w-2xl-auto { width: auto !important; }
.w-2xl-screen { width: 100vw !important; }
.w-2xl-10p { width: 10% !important; }
.w-2xl-20p { width: 20% !important; }
.w-2xl-30p { width: 30% !important; }
.w-2xl-40p { width: 40% !important; }
.w-2xl-50p { width: 50% !important; }
.w-2xl-60p { width: 60% !important; }
.w-2xl-70p { width: 70% !important; }
.w-2xl-80p { width: 80% !important; }
.w-2xl-90p { width: 90% !important; }
.w-2xl-100p { width: 100% !important; }

.h-2xl-none { height: none !important; }
.h-2xl-auto { height: auto !important; }
.h-2xl-screen { height: 100vh !important; }
.h-2xl-10p { height: 10% !important; }
.h-2xl-20p { height: 20% !important; }
.h-2xl-30p { height: 30% !important; }
.h-2xl-40p { height: 40% !important; }
.h-2xl-50p { height: 50% !important; }
.h-2xl-60p { height: 60% !important; }
.h-2xl-70p { height: 70% !important; }
.h-2xl-80p { height: 80% !important; }
.h-2xl-90p { height: 90% !important; }
.h-2xl-100p { height: 100% !important; }
}

@media (max-width: 1440px) {
  .d-2xl-none { display: none !important; }
  .d-2xl-inline { display: inline !important; }
  .d-2xl-inline-block { display: inline-block !important; }
  .d-2xl-block { display: block !important; }
  .d-2xl-flex { display: flex !important; }
  .d-2xl-inline-flex { display: inline-flex !important; }
  .d-2xl-grid { display: grid !important; }

  .grid-cols-2xl-1 { grid-template-columns: repeat(1, 1fr); }
  .grid-cols-2xl-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-cols-2xl-3 { grid-template-columns: repeat(3, 1fr); }
  .grid-cols-2xl-4 { grid-template-columns: repeat(4, 1fr); }
  .grid-cols-2xl-5 { grid-template-columns: repeat(5, 1fr); }
  .grid-cols-2xl-6 { grid-template-columns: repeat(6, 1fr); }
  .grid-cols-2xl-12 { grid-template-columns: repeat(12, 1fr); }
  
  .col-2xl-1 { grid-column: span 1; }
  .col-2xl-2 { grid-column: span 2; }
  .col-2xl-3 { grid-column: span 3; }
  .col-2xl-4 { grid-column: span 4; }
  .col-2xl-5 { grid-column: span 5; }
  .col-2xl-6 { grid-column: span 6; }
  .col-2xl-7 { grid-column: span 7; }
  .col-2xl-8 { grid-column: span 8; }
  .col-2xl-9 { grid-column: span 9; }
  .col-2xl-10 { grid-column: span 10; }
  .col-2xl-11 { grid-column: span 11; }
  .col-2xl-12 { grid-column: span 12; }


  /* Flexbox */
.flex-row-2xl { flex-direction: row !important; }
.flex-column-2xl { flex-direction: column !important; }
.flex-row-reverse-2xl { flex-direction: row-reverse !important; }
.flex-column-reverse-2xl { flex-direction: column-reverse !important; }


.flex-wrap-2xl { flex-wrap: wrap !important; }
.flex-nowrap-2xl { flex-wrap: nowrap !important; }
.flex-wrap-reverse-2xl { flex-wrap: wrap-reverse !important; }

.justify-content-start-2xl { justify-content: flex-start !important; }
.justify-content-end-2xl { justify-content: flex-end !important; }
.justify-content-center-2xl { justify-content: center !important; }
.justify-content-between-2xl { justify-content: space-between !important; }
.justify-content-around-2xl { justify-content: space-around !important; }
.justify-content-evenly-2xl { justify-content: space-evenly !important; }

.align-items-start-2xl { align-items: flex-start !important; }
.align-items-end-2xl { align-items: flex-end !important; }
.align-items-center-2xl { align-items: center !important; }
.align-items-baseline-2xl { align-items: baseline !important; }
.align-items-stretch-2xl { align-items: stretch !important; }

.align-content-start-2xl { align-content: flex-start !important; }
.align-content-end-2xl { align-content: flex-end !important; }
.align-content-center-2xl { align-content: center !important; }
.align-content-between-2xl { align-content: space-between !important; }
.align-content-around-2xl { align-content: space-around !important; }
.align-content-stretch-2xl { align-content: stretch !important; }

.align-self-auto-2xl { align-self: auto !important; }
.align-self-start-2xl { align-self: flex-start !important; }
.align-self-end-2xl { align-self: flex-end !important; }
.align-self-center-2xl { align-self: center !important; }
.align-self-baseline-2xl { align-self: baseline !important; }
.align-self-stretch-2xl { align-self: stretch !important; }

.flex-grow-0-2xl { flex-grow: 0 !important; }
.flex-grow-1-2xl { flex-grow: 1 !important; }
.flex-shrink-0-2xl { flex-shrink: 0 !important; }
.flex-shrink-1-2xl { flex-shrink: 1 !important; }

.flex-1-2xl { flex: 1 1 0% !important; }
.flex-auto-2xl { flex: 1 1 auto !important; }
.flex-initial-2xl { flex: 0 1 auto !important; }
.flex-none-2xl { flex: none !important; }


  /* Width and Height */
.w-2xl-none { width: none !important; }
.w-2xl-auto { width: auto !important; }
.w-2xl-screen { width: 100vw !important; }
.w-2xl-10p { width: 10% !important; }
.w-2xl-20p { width: 20% !important; }
.w-2xl-30p { width: 30% !important; }
.w-2xl-40p { width: 40% !important; }
.w-2xl-50p { width: 50% !important; }
.w-2xl-60p { width: 60% !important; }
.w-2xl-70p { width: 70% !important; }
.w-2xl-80p { width: 80% !important; }
.w-2xl-90p { width: 90% !important; }
.w-2xl-100p { width: 100% !important; }

.h-2xl-none { height: none !important; }
.h-2xl-auto { height: auto !important; }
.h-2xl-screen { height: 100vh !important; }
.h-2xl-10p { height: 10% !important; }
.h-2xl-20p { height: 20% !important; }
.h-2xl-30p { height: 30% !important; }
.h-2xl-40p { height: 40% !important; }
.h-2xl-50p { height: 50% !important; }
.h-2xl-60p { height: 60% !important; }
.h-2xl-70p { height: 70% !important; }
.h-2xl-80p { height: 80% !important; }
.h-2xl-90p { height: 90% !important; }
.h-2xl-100p { height: 100% !important; }
}

@media (max-width: 1280px) {
  .d-xl-none { display: none !important; }
  .d-xl-inline { display: inline !important; }
  .d-xl-inline-block { display: inline-block !important; }
  .d-xl-inline { display: inline !important; }
  .d-xl-inline-block { display: inline-block !important; }
  .d-xl-block { display: block !important; }
  .d-xl-flex { display: flex !important; }
  .d-xl-inline-flex { display: inline-flex !important; }
  .d-xl-grid { display: grid !important; }
  
  .grid-cols-xl-1 { grid-template-columns: repeat(1, 1fr); }
  .grid-cols-xl-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-cols-xl-3 { grid-template-columns: repeat(3, 1fr); }
  .grid-cols-xl-4 { grid-template-columns: repeat(4, 1fr); }
  .grid-cols-xl-5 { grid-template-columns: repeat(5, 1fr); }
  .grid-cols-xl-6 { grid-template-columns: repeat(6, 1fr); }
  .grid-cols-xl-12 { grid-template-columns: repeat(12, 1fr); }
  
  .col-xl-1 { grid-column: span 1; }
  .col-xl-2 { grid-column: span 2; }
  .col-xl-3 { grid-column: span 3; }
  .col-xl-4 { grid-column: span 4; }
  .col-xl-5 { grid-column: span 5; }
  .col-xl-6 { grid-column: span 6; }
  .col-xl-7 { grid-column: span 7; }
  .col-xl-8 { grid-column: span 8; }
  .col-xl-9 { grid-column: span 9; }
  .col-xl-10 { grid-column: span 10; }
  .col-xl-11 { grid-column: span 11; }
  .col-xl-12 { grid-column: span 12; }


  /* Flexbox */
.flex-row-xl { flex-direction: row !important; }
.flex-column-xl { flex-direction: column !important; }
.flex-row-reverse-xl { flex-direction: row-reverse !important; }
.flex-column-reverse-xl { flex-direction: column-reverse !important; }


.flex-wrap-xl { flex-wrap: wrap !important; }
.flex-nowrap-xl { flex-wrap: nowrap !important; }
.flex-wrap-reverse-xl { flex-wrap: wrap-reverse !important; }

.justify-content-start-xl { justify-content: flex-start !important; }
.justify-content-end-xl { justify-content: flex-end !important; }
.justify-content-center-xl { justify-content: center !important; }
.justify-content-between-xl { justify-content: space-between !important; }
.justify-content-around-xl { justify-content: space-around !important; }
.justify-content-evenly-xl { justify-content: space-evenly !important; }

.align-items-start-xl { align-items: flex-start !important; }
.align-items-end-xl { align-items: flex-end !important; }
.align-items-center-xl { align-items: center !important; }
.align-items-baseline-xl { align-items: baseline !important; }
.align-items-stretch-xl { align-items: stretch !important; }

.align-content-start-xl { align-content: flex-start !important; }
.align-content-end-xl { align-content: flex-end !important; }
.align-content-center-xl { align-content: center !important; }
.align-content-between-xl { align-content: space-between !important; }
.align-content-around-xl { align-content: space-around !important; }
.align-content-stretch-xl { align-content: stretch !important; }

.align-self-auto-xl { align-self: auto !important; }
.align-self-start-xl { align-self: flex-start !important; }
.align-self-end-xl { align-self: flex-end !important; }
.align-self-center-xl { align-self: center !important; }
.align-self-baseline-xl { align-self: baseline !important; }
.align-self-stretch-xl { align-self: stretch !important; }

.flex-grow-0-xl { flex-grow: 0 !important; }
.flex-grow-1-xl { flex-grow: 1 !important; }
.flex-shrink-0-xl { flex-shrink: 0 !important; }
.flex-shrink-1-xl { flex-shrink: 1 !important; }

.flex-1-xl { flex: 1 1 0% !important; }
.flex-auto-xl { flex: 1 1 auto !important; }
.flex-initial-xl { flex: 0 1 auto !important; }
.flex-none-xl { flex: none !important; }


  /* Width and Height */
.w-xl-none { width: none !important; }
.w-xl-auto { width: auto !important; }
.w-xl-screen { width: 100vw !important; }
.w-xl-10p { width: 10% !important; }
.w-xl-20p { width: 20% !important; }
.w-xl-30p { width: 30% !important; }
.w-xl-40p { width: 40% !important; }
.w-xl-50p { width: 50% !important; }
.w-xl-60p { width: 60% !important; }
.w-xl-70p { width: 70% !important; }
.w-xl-80p { width: 80% !important; }
.w-xl-90p { width: 90% !important; }
.w-xl-100p { width: 100% !important; }

.h-xl-none { height: none !important; }
.h-xl-auto { height: auto !important; }
.h-xl-screen { height: 100vh !important; }
.h-xl-10p { height: 10% !important; }
.h-xl-20p { height: 20% !important; }
.h-xl-30p { height: 30% !important; }
.h-xl-40p { height: 40% !important; }
.h-xl-50p { height: 50% !important; }
.h-xl-60p { height: 60% !important; }
.h-xl-70p { height: 70% !important; }
.h-xl-80p { height: 80% !important; }
.h-xl-90p { height: 90% !important; }
.h-xl-100p { height: 100% !important; }
}


@media (max-width: 992px) {
  .d-lg-none { display: none !important; }
  .d-lg-inline { display: inline !important; }
  .d-lg-inline-block { display: inline-block !important; }
  .d-lg-block { display: block !important; }
  .d-lg-flex { display: flex !important; }
  .d-lg-inline-flex { display: inline-flex !important; }
  .d-lg-grid { display: grid !important; }
  
  .grid-cols-lg-1 { grid-template-columns: repeat(1, 1fr); }
  .grid-cols-lg-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-cols-lg-3 { grid-template-columns: repeat(3, 1fr); }
  .grid-cols-lg-4 { grid-template-columns: repeat(4, 1fr); }
  .grid-cols-lg-5 { grid-template-columns: repeat(5, 1fr); }
  .grid-cols-lg-6 { grid-template-columns: repeat(6, 1fr); }
  .grid-cols-lg-12 { grid-template-columns: repeat(12, 1fr); }
  
  .col-lg-1 { grid-column: span 1; }
  .col-lg-2 { grid-column: span 2; }
  .col-lg-3 { grid-column: span 3; }
  .col-lg-4 { grid-column: span 4; }
  .col-lg-5 { grid-column: span 5; }
  .col-lg-6 { grid-column: span 6; }
  .col-lg-7 { grid-column: span 7; }
  .col-lg-8 { grid-column: span 8; }
  .col-lg-9 { grid-column: span 9; }
  .col-lg-10 { grid-column: span 10; }
  .col-lg-11 { grid-column: span 11; }
  .col-lg-12 { grid-column: span 12; }


  /* Flexbox */
.flex-row-lg { flex-direction: row !important; }
.flex-column-lg { flex-direction: column !important; }
.flex-row-reverse-lg { flex-direction: row-reverse !important; }
.flex-column-reverse-lg { flex-direction: column-reverse !important; }


.flex-wrap-lg { flex-wrap: wrap !important; }
.flex-nowrap-lg { flex-wrap: nowrap !important; }
.flex-wrap-reverse-lg { flex-wrap: wrap-reverse !important; }

.justify-content-start-lg { justify-content: flex-start !important; }
.justify-content-end-lg { justify-content: flex-end !important; }
.justify-content-center-lg { justify-content: center !important; }
.justify-content-between-lg { justify-content: space-between !important; }
.justify-content-around-lg { justify-content: space-around !important; }
.justify-content-evenly-lg { justify-content: space-evenly !important; }

.align-items-start-lg { align-items: flex-start !important; }
.align-items-end-lg { align-items: flex-end !important; }
.align-items-center-lg { align-items: center !important; }
.align-items-baseline-lg { align-items: baseline !important; }
.align-items-stretch-lg { align-items: stretch !important; }

.align-content-start-lg { align-content: flex-start !important; }
.align-content-end-lg { align-content: flex-end !important; }
.align-content-center-lg { align-content: center !important; }
.align-content-between-lg { align-content: space-between !important; }
.align-content-around-lg { align-content: space-around !important; }
.align-content-stretch-lg { align-content: stretch !important; }

.align-self-auto-lg { align-self: auto !important; }
.align-self-start-lg { align-self: flex-start !important; }
.align-self-end-lg { align-self: flex-end !important; }
.align-self-center-lg { align-self: center !important; }
.align-self-baseline-lg { align-self: baseline !important; }
.align-self-stretch-lg { align-self: stretch !important; }

.flex-grow-0-lg { flex-grow: 0 !important; }
.flex-grow-1-lg { flex-grow: 1 !important; }
.flex-shrink-0-lg { flex-shrink: 0 !important; }
.flex-shrink-1-lg { flex-shrink: 1 !important; }

.flex-1-lg { flex: 1 1 0% !important; }
.flex-auto-lg { flex: 1 1 auto !important; }
.flex-initial-lg { flex: 0 1 auto !important; }
.flex-none-lg { flex: none !important; }

  /* Width and Height */
.w-lg-none { width: none !important; }
.w-lg-auto { width: auto !important; }
.w-lg-screen { width: 100vw !important; }
.w-lg-10p { width: 10% !important; }
.w-lg-20p { width: 20% !important; }
.w-lg-30p { width: 30% !important; }
.w-lg-40p { width: 40% !important; }
.w-lg-50p { width: 50% !important; }
.w-lg-60p { width: 60% !important; }
.w-lg-70p { width: 70% !important; }
.w-lg-80p { width: 80% !important; }
.w-lg-90p { width: 90% !important; }
.w-lg-100p { width: 100% !important; }

.h-lg-none { height: none !important; }
.h-lg-auto { height: auto !important; }
.h-lg-screen { height: 100vh !important; }
.h-lg-10p { height: 10% !important; }
.h-lg-20p { height: 20% !important; }
.h-lg-30p { height: 30% !important; }
.h-lg-40p { height: 40% !important; }
.h-lg-50p { height: 50% !important; }
.h-lg-60p { height: 60% !important; }
.h-lg-70p { height: 70% !important; }
.h-lg-80p { height: 80% !important; }
.h-lg-90p { height: 90% !important; }
.h-lg-100p { height: 100% !important; }
}




@media (max-width: 768px) {
  .d-md-none { display: none !important; }
  .d-md-inline { display: inline !important; }
  .d-md-inline-block { display: inline-block !important; }
  .d-md-block { display: block !important; }
  .d-md-flex { display: flex !important; }
  .d-md-inline-flex { display: inline-flex !important; }
  .d-md-grid { display: grid !important; }
  
  .grid-cols-md-1 { grid-template-columns: repeat(1, 1fr); }
  .grid-cols-md-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-cols-md-3 { grid-template-columns: repeat(3, 1fr); }
  .grid-cols-md-4 { grid-template-columns: repeat(4, 1fr); }
  .grid-cols-md-5 { grid-template-columns: repeat(5, 1fr); }
  .grid-cols-md-6 { grid-template-columns: repeat(6, 1fr); }
  .grid-cols-md-12 { grid-template-columns: repeat(12, 1fr); }
  
  .col-md-1 { grid-column: span 1; }
  .col-md-2 { grid-column: span 2; }
  .col-md-3 { grid-column: span 3; }
  .col-md-4 { grid-column: span 4; }
  .col-md-5 { grid-column: span 5; }
  .col-md-6 { grid-column: span 6; }
  .col-md-7 { grid-column: span 7; }
  .col-md-8 { grid-column: span 8; }
  .col-md-9 { grid-column: span 9; }
  .col-md-10 { grid-column: span 10; }
  .col-md-11 { grid-column: span 11; }
  .col-md-12 { grid-column: span 12; }


/* Flexbox */
.flex-row-md { flex-direction: row !important; }
.flex-column-md { flex-direction: column !important; }
.flex-row-reverse-md { flex-direction: row-reverse !important; }
.flex-column-reverse-md { flex-direction: column-reverse !important; }


.flex-wrap-md { flex-wrap: wrap !important; }
.flex-nowrap-md { flex-wrap: nowrap !important; }
.flex-wrap-reverse-md { flex-wrap: wrap-reverse !important; }

.justify-content-start-md { justify-content: flex-start !important; }
.justify-content-end-md { justify-content: flex-end !important; }
.justify-content-center-md { justify-content: center !important; }
.justify-content-between-md { justify-content: space-between !important; }
.justify-content-around-md { justify-content: space-around !important; }
.justify-content-evenly-md { justify-content: space-evenly !important; }

.align-items-start-md { align-items: flex-start !important; }
.align-items-end-md { align-items: flex-end !important; }
.align-items-center-md { align-items: center !important; }
.align-items-baseline-md { align-items: baseline !important; }
.align-items-stretch-md { align-items: stretch !important; }

.align-content-start-md { align-content: flex-start !important; }
.align-content-end-md { align-content: flex-end !important; }
.align-content-center-md { align-content: center !important; }
.align-content-between-md { align-content: space-between !important; }
.align-content-around-md { align-content: space-around !important; }
.align-content-stretch-md { align-content: stretch !important; }

.align-self-auto-md { align-self: auto !important; }
.align-self-start-md { align-self: flex-start !important; }
.align-self-end-md { align-self: flex-end !important; }
.align-self-center-md { align-self: center !important; }
.align-self-baseline-md { align-self: baseline !important; }
.align-self-stretch-md { align-self: stretch !important; }

.flex-grow-0-md { flex-grow: 0 !important; }
.flex-grow-1-md { flex-grow: 1 !important; }
.flex-shrink-0-md { flex-shrink: 0 !important; }
.flex-shrink-1-md { flex-shrink: 1 !important; }

.flex-1-md { flex: 1 1 0% !important; }
.flex-auto-md { flex: 1 1 auto !important; }
.flex-initial-md { flex: 0 1 auto !important; }
.flex-none-md { flex: none !important; }

  /* Width and Height */
.w-md-none { width: none !important; }
.w-md-auto { width: auto !important; }
.w-md-screen { width: 100vw !important; }
.w-md-10p { width: 10% !important; }
.w-md-20p { width: 20% !important; }
.w-md-30p { width: 30% !important; }
.w-md-40p { width: 40% !important; }
.w-md-50p { width: 50% !important; }
.w-md-60p { width: 60% !important; }
.w-md-70p { width: 70% !important; }
.w-md-80p { width: 80% !important; }
.w-md-90p { width: 90% !important; }
.w-md-100p { width: 100% !important; }

.h-md-none { height: none !important; }
.h-md-auto { height: auto !important; }
.h-md-screen { height: 100vh !important; }
.h-md-10p { height: 10% !important; }
.h-md-20p { height: 20% !important; }
.h-md-30p { height: 30% !important; }
.h-md-40p { height: 40% !important; }
.h-md-50p { height: 50% !important; }
.h-md-60p { height: 60% !important; }
.h-md-70p { height: 70% !important; }
.h-md-80p { height: 80% !important; }
.h-md-90p { height: 90% !important; }
.h-md-100p { height: 100% !important; }
}


@media (max-width: 567px) {
  .d-sm-none { display: none !important; }
  .d-sm-inline { display: inline !important; }
  .d-sm-inline-block { display: inline-block !important; }
  .d-sm-block { display: block !important; }
  .d-sm-flex { display: flex !important; }
  .d-sm-inline-flex { display: inline-flex !important; }
  .d-sm-grid { display: grid !important; }
  
  .grid-cols-sm-1 { grid-template-columns: repeat(1, 1fr); }
  .grid-cols-sm-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-cols-sm-3 { grid-template-columns: repeat(3, 1fr); }
  .grid-cols-sm-4 { grid-template-columns: repeat(4, 1fr); }
  .grid-cols-sm-5 { grid-template-columns: repeat(5, 1fr); }
  .grid-cols-sm-6 { grid-template-columns: repeat(6, 1fr); }
  .grid-cols-sm-12 { grid-template-columns: repeat(12, 1fr); }
  
  .col-sm-1 { grid-column: span 1; }
  .col-sm-2 { grid-column: span 2; }
  .col-sm-3 { grid-column: span 3; }
  .col-sm-4 { grid-column: span 4; }
  .col-sm-5 { grid-column: span 5; }
  .col-sm-6 { grid-column: span 6; }
  .col-sm-7 { grid-column: span 7; }
  .col-sm-8 { grid-column: span 8; }
  .col-sm-9 { grid-column: span 9; }
  .col-sm-10 { grid-column: span 10; }
  .col-sm-11 { grid-column: span 11; }
  .col-sm-12 { grid-column: span 12; }


  /* Flexbox */
.flex-row-sm { flex-direction: row !important; }
.flex-column-sm { flex-direction: column !important; }
.flex-row-reverse-sm { flex-direction: row-reverse !important; }
.flex-column-reverse-sm { flex-direction: column-reverse !important; }


.flex-wrap-sm { flex-wrap: wrap !important; }
.flex-nowrap-sm { flex-wrap: nowrap !important; }
.flex-wrap-reverse-sm { flex-wrap: wrap-reverse !important; }

.justify-content-start-sm { justify-content: flex-start !important; }
.justify-content-end-sm { justify-content: flex-end !important; }
.justify-content-center-sm { justify-content: center !important; }
.justify-content-between-sm { justify-content: space-between !important; }
.justify-content-around-sm { justify-content: space-around !important; }
.justify-content-evenly-sm { justify-content: space-evenly !important; }

.align-items-start-sm { align-items: flex-start !important; }
.align-items-end-sm { align-items: flex-end !important; }
.align-items-center-sm { align-items: center !important; }
.align-items-baseline-sm { align-items: baseline !important; }
.align-items-stretch-sm { align-items: stretch !important; }

.align-content-start-sm { align-content: flex-start !important; }
.align-content-end-sm { align-content: flex-end !important; }
.align-content-center-sm { align-content: center !important; }
.align-content-between-sm { align-content: space-between !important; }
.align-content-around-sm { align-content: space-around !important; }
.align-content-stretch-sm { align-content: stretch !important; }

.align-self-auto-sm { align-self: auto !important; }
.align-self-start-sm { align-self: flex-start !important; }
.align-self-end-sm { align-self: flex-end !important; }
.align-self-center-sm { align-self: center !important; }
.align-self-baseline-sm { align-self: baseline !important; }
.align-self-stretch-sm { align-self: stretch !important; }

.flex-grow-0-sm { flex-grow: 0 !important; }
.flex-grow-1-sm { flex-grow: 1 !important; }
.flex-shrink-0-sm { flex-shrink: 0 !important; }
.flex-shrink-1-sm { flex-shrink: 1 !important; }

.flex-1-sm { flex: 1 1 0% !important; }
.flex-auto-sm { flex: 1 1 auto !important; }
.flex-initial-sm { flex: 0 1 auto !important; }
.flex-none-sm { flex: none !important; }



  /* Width and Height */
.w-sm-none { width: none !important; }
.w-sm-auto { width: auto !important; }
.w-sm-screen { width: 100vw !important; }
.w-sm-10p { width: 10% !important; }
.w-sm-20p { width: 20% !important; }
.w-sm-30p { width: 30% !important; }
.w-sm-40p { width: 40% !important; }
.w-sm-50p { width: 50% !important; }
.w-sm-60p { width: 60% !important; }
.w-sm-70p { width: 70% !important; }
.w-sm-80p { width: 80% !important; }
.w-sm-90p { width: 90% !important; }
.w-sm-100p { width: 100% !important; }

.h-sm-none { height: none !important; }
.h-sm-auto { height: auto !important; }
.h-sm-screen { height: 100vh !important; }
.h-sm-10p { height: 10% !important; }
.h-sm-20p { height: 20% !important; }
.h-sm-30p { height: 30% !important; }
.h-sm-40p { height: 40% !important; }
.h-sm-50p { height: 50% !important; }
.h-sm-60p { height: 60% !important; }
.h-sm-70p { height: 70% !important; }
.h-sm-80p { height: 80% !important; }
.h-sm-90p { height: 90% !important; }
.h-sm-100p { height: 100% !important; }
}

/* Fallbacks for older browsers */
@supports not (display: grid) {
  .grid-masonry {
    column-count: auto;
    column-width: var(--mico-min-column-width);
  }
  .grid-masonry > * {
    display: inline-block;
    width: 100%;
  }
  
  .grid {
    display: flex;
    flex-wrap: wrap;
    margin: calc(-1 * var(--mico-grid-gap));
  }


  /* Grid item styles */
  .grid > * {
    flex-basis: calc((100% / var(--mico-grid-columns)) - (2 * var(--mico-grid-gap)));
    margin: var(--mico-grid-gap);
  }

  /* Responsive grid items */
  @media (max-width: 768px) {
    .grid > * {
      flex-basis: calc(50% - (2 * var(--mico-grid-gap)));
    }
  }

  @media (max-width: 480px) {
    .grid > * {
      flex-basis: 100%;
    }
  }

}