const MicoEasing={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",easeInQuad:"cubic-bezier(0.55, 0.085, 0.68, 0.53)",easeOutQuad:"cubic-bezier(0.25, 0.46, 0.45, 0.94)",easeInOutQuad:"cubic-bezier(0.455, 0.03, 0.515, 0.955)",easeInCubic:"cubic-bezier(0.55, 0.055, 0.675, 0.19)",easeOutCubic:"cubic-bezier(0.215, 0.61, 0.355, 1)",easeInOutCubic:"cubic-bezier(0.645, 0.045, 0.355, 1)",easeInBack:"cubic-bezier(0.6, -0.28, 0.735, 0.045)",easeOutBack:"cubic-bezier(0.175, 0.885, 0.32, 1.275)",easeInOutBack:"cubic-bezier(0.68, -0.55, 0.265, 1.55)",easeOutBounce:"cubic-bezier(0.68, -0.55, 0.265, 1.55)",easeOutElastic:"cubic-bezier(0.68, -0.55, 0.265, 1.55)"},MicoDuration={instant:0,fast:200,normal:300,slow:500,slower:700,slowest:1e3},MicoAnimationUtils={animate(e,t,i={}){if(!e)return Promise.reject("Element not found");const{duration:n=MicoDuration.normal,easing:o=MicoEasing.easeOut,delay:s=0,fill:a="both"}=i;return new Promise((i=>{t.from&&Object.assign(e.style,t.from),setTimeout((()=>{e.style.transition=`all ${n}ms ${o}`,t.to&&Object.assign(e.style,t.to),setTimeout((()=>{"forwards"!==a&&(e.style.transition=""),i(e)}),n)}),s)}))},fadeIn(e,t={}){return this.animate(e,{from:{opacity:"0"},to:{opacity:"1"}},t)},fadeOut(e,t={}){return this.animate(e,{from:{opacity:"1"},to:{opacity:"0"}},t)},slideIn(e,t="up",i={}){const n={up:"translateY(100%)",down:"translateY(-100%)",left:"translateX(100%)",right:"translateX(-100%)"};return this.animate(e,{from:{opacity:"0",transform:n[t]||n.up},to:{opacity:"1",transform:"translate(0, 0)"}},i)},scale(e,t=0,i=1,n={}){return this.animate(e,{from:{opacity:"0",transform:`scale(${t})`},to:{opacity:"1",transform:`scale(${i})`}},n)},bounce(e,t={}){const i={duration:t.duration||MicoDuration.normal,easing:MicoEasing.easeOutBounce,iterations:t.iterations||1};return e.animate([{transform:"translateY(0)",offset:0},{transform:"translateY(-30px)",offset:.5},{transform:"translateY(0)",offset:1}],i)},shake(e,t={}){const i={duration:t.duration||500,easing:MicoEasing.linear,iterations:t.iterations||1};return e.animate([{transform:"translateX(0)",offset:0},{transform:"translateX(-10px)",offset:.1},{transform:"translateX(10px)",offset:.2},{transform:"translateX(-10px)",offset:.3},{transform:"translateX(10px)",offset:.4},{transform:"translateX(-10px)",offset:.5},{transform:"translateX(10px)",offset:.6},{transform:"translateX(-10px)",offset:.7},{transform:"translateX(10px)",offset:.8},{transform:"translateX(-10px)",offset:.9},{transform:"translateX(0)",offset:1}],i)},pulse(e,t={}){const i={duration:t.duration||MicoDuration.slow,easing:MicoEasing.easeInOut,iterations:t.iterations||"infinite"};return e.animate([{opacity:"1",transform:"scale(1)",offset:0},{opacity:"0.7",transform:"scale(1.05)",offset:.5},{opacity:"1",transform:"scale(1)",offset:1}],i)},stagger(e,t,i={}){const{delay:n=100,...o}=i,s=[];return e.forEach(((e,i)=>{const a=(o.delay||0)+n*i,r=t(e,{...o,delay:a});s.push(r)})),Promise.all(s)},chain:e=>e.reduce(((e,t)=>e.then((()=>t()))),Promise.resolve()),parallax(e,t={}){const{speed:i=.5,direction:n="vertical"}=t;let o=!1;const s=()=>{o||(requestAnimationFrame((()=>{(()=>{const t=window.pageYOffset*-i;e.style.transform="vertical"===n?`translateY(${t}px)`:`translateX(${t}px)`})(),o=!1})),o=!0)};return window.addEventListener("scroll",s),()=>{window.removeEventListener("scroll",s)}},isInViewport(e,t=0){const i=e.getBoundingClientRect(),n=window.innerHeight||document.documentElement.clientHeight,o=window.innerWidth||document.documentElement.clientWidth;return i.top>=-t&&i.left>=-t&&i.bottom<=n+t&&i.right<=o+t},waitForAnimation:e=>new Promise((t=>{const i=()=>{e.removeEventListener("animationend",i),e.removeEventListener("transitionend",i),t(e)};e.addEventListener("animationend",i),e.addEventListener("transitionend",i)})),raf:e=>requestAnimationFrame(e),cancelRaf:e=>cancelAnimationFrame(e)};"undefined"!=typeof module&&module.exports&&(module.exports={MicoEasing:MicoEasing,MicoDuration:MicoDuration,MicoAnimationUtils:MicoAnimationUtils}),"undefined"!=typeof window&&(window.MicoEasing=MicoEasing,window.MicoDuration=MicoDuration,window.MicoAnimationUtils=MicoAnimationUtils);class MicoAnimationEngine{constructor(e={}){this.options={rootMargin:e.rootMargin||"0px 0px -10% 0px",threshold:e.threshold||.1,defaultDuration:e.defaultDuration||800,defaultEasing:e.defaultEasing||"ease-out",enableScrollAnimations:!1!==e.enableScrollAnimations,enableRippleEffects:!1!==e.enableRippleEffects,enableTypewriter:!1!==e.enableTypewriter,scrollAnimationSelector:e.scrollAnimationSelector||".animate-on-scroll",rippleSelector:e.rippleSelector||".btn-ripple",typewriterSelector:e.typewriterSelector||".anim-typewriter",debug:e.debug||!1},this.isReducedMotion=window.matchMedia("(prefers-reduced-motion: reduce)").matches,this.intersectionObserver=null,this.animatedElements=new Set,this.init()}init(){this.options.debug&&console.log("🎬 Mico Animation Engine initializing..."),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(()=>this.setup())):this.setup()}setup(){this.isReducedMotion?this.disableAnimations():(this.options.enableScrollAnimations&&this.initScrollAnimations(),this.options.enableRippleEffects&&this.initRippleEffects(),this.options.enableTypewriter&&this.initTypewriterEffects(),window.matchMedia("(prefers-reduced-motion: reduce)").addEventListener("change",(e=>{this.isReducedMotion=e.matches,e.matches?this.disableAnimations():this.setup()})),this.options.debug&&console.log("✅ Mico Animation Engine initialized"))}initScrollAnimations(){const e=document.querySelectorAll(this.options.scrollAnimationSelector);0!==e.length&&(this.intersectionObserver=new IntersectionObserver((e=>{e.forEach((e=>{e.isIntersecting&&!this.animatedElements.has(e.target)&&(this.triggerScrollAnimation(e.target),this.animatedElements.add(e.target))}))}),{rootMargin:this.options.rootMargin,threshold:this.options.threshold}),e.forEach((e=>{this.intersectionObserver.observe(e)})),this.options.debug&&console.log(`🔍 Observing ${e.length} scroll animation elements`))}triggerScrollAnimation(e){e.classList.add("is-visible");const t=e.dataset.animDuration;t&&(e.style.animationDuration=t+"ms");const i=e.dataset.animDelay;i&&(e.style.animationDelay=i+"ms"),this.options.debug&&console.log("🎭 Triggered scroll animation for:",e)}initRippleEffects(){const e=document.querySelectorAll(this.options.rippleSelector);e.forEach((e=>{e.addEventListener("click",(e=>this.createRipple(e))),e.addEventListener("touchstart",(e=>this.createRipple(e)))})),this.options.debug&&console.log(`💧 Initialized ripple effects for ${e.length} elements`)}createRipple(e){const t=e.currentTarget,i=t.getBoundingClientRect(),n=e.clientX-i.left,o=e.clientY-i.top,s=document.createElement("span");s.className="ripple-element",s.style.left=n+"px",s.style.top=o+"px",t.appendChild(s),setTimeout((()=>{s.parentNode&&s.parentNode.removeChild(s)}),600)}initTypewriterEffects(){const e=document.querySelectorAll(this.options.typewriterSelector);e.forEach((e=>{const t=e.dataset.typeText||e.textContent,i=parseInt(e.dataset.typeSpeed)||50,n=parseInt(e.dataset.typeDelay)||0;t&&setTimeout((()=>{this.typewriterEffect(e,t,i)}),n)})),this.options.debug&&console.log(`⌨️ Initialized typewriter effects for ${e.length} elements`)}typewriterEffect(e,t,i){e.textContent="",e.classList.add("typewriter-cursor");let n=0;const o=setInterval((()=>{n<t.length?(e.textContent+=t.charAt(n),n++):(clearInterval(o),setTimeout((()=>{e.classList.remove("typewriter-cursor")}),2e3))}),i)}disableAnimations(){this.intersectionObserver&&this.intersectionObserver.disconnect();document.querySelectorAll(this.options.scrollAnimationSelector).forEach((e=>{e.classList.add("is-visible"),e.style.opacity="1",e.style.transform="none"})),this.options.debug&&console.log("🚫 Animations disabled due to reduced motion preference")}animate(e,t,i={}){if(this.isReducedMotion)return;i.duration||this.options.defaultDuration;const n=i.delay||0;setTimeout((()=>{e.classList.add("animate-on-scroll",t),this.triggerScrollAnimation(e)}),n)}observe(e){if(!this.intersectionObserver)return;(Array.isArray(e)?e:[e]).forEach((e=>{e.classList.contains(this.options.scrollAnimationSelector.slice(1))&&this.intersectionObserver.observe(e)}))}unobserve(e){if(!this.intersectionObserver)return;(Array.isArray(e)?e:[e]).forEach((e=>{this.intersectionObserver.unobserve(e),this.animatedElements.delete(e)}))}destroy(){this.intersectionObserver&&this.intersectionObserver.disconnect(),this.animatedElements.clear(),this.options.debug&&console.log("🗑️ Mico Animation Engine destroyed")}}"undefined"==typeof module&&document.addEventListener("DOMContentLoaded",(()=>{window.MicoAnimation=new MicoAnimationEngine})),"undefined"!=typeof module&&module.exports&&(module.exports=MicoAnimationEngine);class MicoFramework{constructor(e={}){this.version="1.0.0",this.options={animation:{enabled:!1!==e.animation?.enabled,...e.animation},debug:e.debug||!1,autoInit:!1!==e.autoInit},this.components={},this.animationEngine=null,this.options.autoInit&&this.init()}init(){this.options.debug&&console.log(`🚀 Mico Framework v${this.version} initializing...`),this.options.animation.enabled&&this.initAnimationEngine(),this.initUtilities(),document.documentElement.setAttribute("data-mico-initialized","true"),this.options.debug&&console.log("✅ Mico Framework initialized successfully")}initAnimationEngine(){void 0!==MicoAnimationEngine?this.animationEngine=new MicoAnimationEngine({...this.options.animation,debug:this.options.debug}):this.options.debug&&console.warn("⚠️ MicoAnimationEngine not found. Animation features disabled.")}initUtilities(){this.initFormEnhancements(),this.initAccessibilityFeatures(),this.initResponsiveUtilities()}initFormEnhancements(){document.querySelectorAll("textarea[data-auto-resize]").forEach((e=>{this.autoResizeTextarea(e)}));document.querySelectorAll('input[type="file"][data-enhanced]').forEach((e=>{this.enhanceFileInput(e)}))}autoResizeTextarea(e){const t=()=>{e.style.height="auto",e.style.height=e.scrollHeight+"px"};e.addEventListener("input",t),e.addEventListener("change",t),t()}enhanceFileInput(e){const t=document.createElement("div");t.className="file-input-wrapper";const i=document.createElement("label");i.className="file-input-label btn btn-outline",i.textContent=e.dataset.label||"Choose File",i.setAttribute("for",e.id);const n=document.createElement("span");n.className="file-input-feedback",n.textContent="No file chosen",e.parentNode.insertBefore(t,e),t.appendChild(e),t.appendChild(i),t.appendChild(n),e.addEventListener("change",(e=>{const t=e.target.files;t.length>0?n.textContent=1===t.length?t[0].name:`${t.length} files selected`:n.textContent="No file chosen"}))}initAccessibilityFeatures(){this.initFocusManagement(),this.initKeyboardNavigation(),this.initAriaEnhancements()}initFocusManagement(){document.querySelectorAll("[data-modal]").forEach((e=>{this.createFocusTrap(e)}));document.querySelectorAll(".skip-link").forEach((e=>{e.addEventListener("click",(t=>{t.preventDefault();const i=document.querySelector(e.getAttribute("href"));i&&(i.focus(),i.scrollIntoView({behavior:"smooth"}))}))}))}createFocusTrap(e){const t=e.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');if(0===t.length)return;const i=t[0],n=t[t.length-1];e.addEventListener("keydown",(e=>{"Tab"===e.key&&(e.shiftKey?document.activeElement===i&&(e.preventDefault(),n.focus()):document.activeElement===n&&(e.preventDefault(),i.focus()))}))}initKeyboardNavigation(){document.querySelectorAll(".btn-group").forEach((e=>{this.addArrowKeyNavigation(e,"button")}));document.querySelectorAll('[role="tablist"]').forEach((e=>{this.addArrowKeyNavigation(e,'[role="tab"]')}))}addArrowKeyNavigation(e,t){const i=e.querySelectorAll(t);i.forEach(((e,t)=>{e.addEventListener("keydown",(e=>{let n;switch(e.key){case"ArrowRight":case"ArrowDown":e.preventDefault(),n=(t+1)%i.length;break;case"ArrowLeft":case"ArrowUp":e.preventDefault(),n=(t-1+i.length)%i.length;break;case"Home":e.preventDefault(),n=0;break;case"End":e.preventDefault(),n=i.length-1;break;default:return}i[n].focus()}))}))}initAriaEnhancements(){document.querySelectorAll("button:not([aria-label]):not([aria-labelledby])").forEach((e=>{if(!e.textContent.trim()){const t=e.getAttribute("title");t&&e.setAttribute("aria-label",t)}}))}initResponsiveUtilities(){this.initResponsiveImages(),this.updateViewportClasses(),window.addEventListener("resize",(()=>this.updateViewportClasses()))}initResponsiveImages(){const e=document.querySelectorAll("img[data-src]");if("IntersectionObserver"in window){const t=new IntersectionObserver((e=>{e.forEach((e=>{if(e.isIntersecting){const i=e.target;i.src=i.dataset.src,i.removeAttribute("data-src"),t.unobserve(i)}}))}));e.forEach((e=>t.observe(e)))}else e.forEach((e=>{e.src=e.dataset.src,e.removeAttribute("data-src")}))}updateViewportClasses(){const e=window.innerWidth,t=window.innerHeight,i=document.documentElement;i.classList.remove("viewport-xs","viewport-sm","viewport-md","viewport-lg","viewport-xl"),e<576?i.classList.add("viewport-xs"):e<768?i.classList.add("viewport-sm"):e<992?i.classList.add("viewport-md"):e<1200?i.classList.add("viewport-lg"):i.classList.add("viewport-xl"),i.classList.toggle("viewport-landscape",e>t),i.classList.toggle("viewport-portrait",e<=t)}animate(e,t,i){if(this.animationEngine)return this.animationEngine.animate(e,t,i)}utils(){return MicoAnimationUtils}getVersion(){return this.version}destroy(){this.animationEngine&&this.animationEngine.destroy(),document.documentElement.removeAttribute("data-mico-initialized"),this.options.debug&&console.log("🗑️ Mico Framework destroyed")}}"undefined"==typeof module&&("loading"===document.readyState?document.addEventListener("DOMContentLoaded",(()=>{window.Mico=new MicoFramework})):window.Mico=new MicoFramework),"undefined"!=typeof module&&module.exports&&(module.exports=MicoFramework);