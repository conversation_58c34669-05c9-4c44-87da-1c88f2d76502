/**
 * Mico CSS Framework
 * A lightweight and versatile CSS framework focused on utility classes
 * Version: 1.0.0
 * Author: <PERSON>
 */

/* Base and Theming */
@import "base/variables.css";

/* Core Utilities */
@import "utils/typography/typography.css";
@import "utils/colors/colors.css";
@import "utils/layout/layout.css";
@import "utils/layout/spacing.css";
@import "utils/borders/borders.css";
@import "utils/buttons/buttons.css";
@import "utils/states/states.css";
@import "utils/animation/animation.css";
@import "utils/miscellaneous/misc.css";

/* Presets */
@import "presets/oxygenbuilder.pre.css";

/* Accessibility */
@import "accessibility/accessibility.css";