/**
 * Mico CSS Framework - Core Variables
 *
 * This file defines the foundational CSS variables that power the Mico CSS framework.
 * These variables create a consistent design system that can be used throughout your project.
 *
 * USAGE:
 * Variables are accessed using the var() function:
 * Example: font-size: var(--mico-text-md);
 */

/* ========================================================================== */
/* RESPONSIVE BREAKPOINTS                                                     */
/* ========================================================================== */

:root {
  /**
   * Breakpoints define the viewport width thresholds for responsive design.
   * These values align with common device sizes and provide a consistent
   * foundation for responsive layouts.
   *
   * Usage: @media (min-width: var(--mico-breakpoint-md)) { ... }
   */
  --mico-breakpoint-xs: 490px;   /* Extra small devices (phones) */
  --mico-breakpoint-sm: 576px;   /* Small devices (large phones, portrait tablets) */
  --mico-breakpoint-md: 768px;   /* Medium devices (tablets) */
  --mico-breakpoint-lg: 992px;   /* Large devices (desktops) */
  --mico-breakpoint-xl: 1280px;  /* Extra large devices (large desktops) */
  --mico-breakpoint-2xl: 1440px; /* Ultra large devices (wide screens) */
  --mico-breakpoint-3xl: 1600px;  /* Super large devices (wider screens) */


  /* ====================================================================== */
  /* COMMON VALUES SYSTEM                                                   */
  /* ====================================================================== */

  /**
   * Common Values
   *
   * These variables define frequently used values to ensure consistency
   * and reduce repetition throughout the framework.
   */
  --mico-value-auto: auto;
  --mico-value-none: none;
  --mico-value-normal: normal;
  --mico-value-inherit: inherit;
  --mico-value-initial: initial;
  --mico-value-unset: unset;
  --mico-value-current: currentColor;
  --mico-value-transparent: transparent;
  --mico-value-0: 0;

  /* ====================================================================== */
  /* TYPOGRAPHY SYSTEM                                                      */
  /* ====================================================================== */

  /**
   * Font Families
   *
   * Standard font stacks for different text purposes.
   * These provide fallbacks for consistent typography across platforms.
   */
  --mico-font-sans: system-ui, -apple-system, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  --mico-font-serif: Georgia, Cambria, 'Times New Roman', Times, serif;
  --mico-font-mono: 'SFMono-Regular', Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  --mico-font-display: var(--mico-font-sans); /* Can be customized in styleguide.css */
  --mico-font-body: var(--mico-font-sans);    /* Can be customized in styleguide.css */

  /**
   * Font Sizes using clamp() for responsive scaling
   *
   * The clamp() function takes three values: minimum, preferred, and maximum
   * This creates text that scales smoothly between viewport sizes while
   * maintaining readable minimum and maximum sizes.
   *
   * Format: clamp(min-size, viewport-based-size, max-size)
   */
  --mico-fs-xs: clamp(0.75rem, 1.5vw, 0.975rem); /* Extra small text, captions */
  --mico-fs-sm: clamp(0.975rem, 2vw, 1.1rem);    /* Small text */
  --mico-fs-md: clamp(1rem, 2.5vw, 1.50rem);  /* Standard body text */
  --mico-fs-lg: clamp(1.25rem, 3vw, 1.875rem); /* Small headings (h4) */
  --mico-fs-xl: clamp(1.5rem, 4vw, 2.25rem);   /* Medium headings (h3) */
  --mico-fs-2xl: clamp(1.875rem, 5vw, 3rem);   /* Large headings (h2) */
  --mico-fs-3xl: clamp(2.25rem, 5.5vw, 3.5rem); /* Large headings (h1) */
  --mico-fs-4xl: clamp(2.5rem, 6vw, 4rem);     /* Large headings */
  --mico-fs-5xl: clamp(3rem, 6.5vw, 4.5rem);   /* Large headings */
  --mico-fs-6xl: clamp(3.5rem, 7vw, 5rem);     /* Large headings */
  --mico-fs-7xl: clamp(3.75rem, 7.5vw, 5.5rem); /* Very large headings */
  --mico-fs-8xl: clamp(4rem, 8vw, 6rem);       /* Extra massive headings */
  --mico-fs-9xl: clamp(4.5rem, 9vw, 7rem);     /* Massive headings */

  /**
   * Font Weights
   *
   * Standard font weight values from 100 (thinnest) to 900 (boldest)
   * Using consistent naming pattern with descriptive suffixes
   */
  --mico-fw-100: 100;  /* Thinnest */
  --mico-fw-200: 200;  /* Extra light */
  --mico-fw-300: 300;  /* Light */
  --mico-fw-400: 400;  /* Normal/Regular */
  --mico-fw-500: 500;  /* Medium */
  --mico-fw-600: 600;  /* Semi bold */
  --mico-fw-700: 700;  /* Bold */
  --mico-fw-800: 800;  /* Extra bold */
  --mico-fw-900: 900;  /* Black/Heaviest */

  /**
   * Font Stretch Properties
   *
   * Controls the width of glyphs if the font family has variable widths.
   * Values represent percentage of normal width.
   */
  --mico-font-stretch-ultra-condensed: ultra-condensed; /* 50% */
  --mico-font-stretch-extra-condensed: extra-condensed; /* 62.5% */
  --mico-font-stretch-condensed: condensed;             /* 75% */
  --mico-font-stretch-semi-condensed: semi-condensed;   /* 87.5% */
  --mico-font-stretch-normal: var(--mico-value-normal); /* 100% */
  --mico-font-stretch-semi-expanded: semi-expanded;     /* 112.5% */
  --mico-font-stretch-expanded: expanded;               /* 125% */
  --mico-font-stretch-extra-expanded: extra-expanded;   /* 150% */
  --mico-font-stretch-ultra-expanded: ultra-expanded;   /* 200% */

  /**
   * Font Style Properties
   *
   * Standard font style values for consistent usage.
   */
  --mico-font-style-normal: var(--mico-value-normal);
  --mico-font-style-italic: italic;

  /**
   * Font Variant Numeric Properties
   *
   * Controls numeric font features for enhanced typography.
   */
  --mico-font-variant-numeric-normal: var(--mico-value-normal);
  --mico-font-variant-numeric-ordinal: ordinal;
  --mico-font-variant-numeric-slashed-zero: slashed-zero;
  --mico-font-variant-numeric-lining-nums: lining-nums;
  --mico-font-variant-numeric-oldstyle-nums: oldstyle-nums;
  --mico-font-variant-numeric-proportional-nums: proportional-nums;
  --mico-font-variant-numeric-tabular-nums: tabular-nums;
  --mico-font-variant-numeric-diagonal-fractions: diagonal-fractions;
  --mico-font-variant-numeric-stacked-fractions: stacked-fractions;

  /**
   * Font Variant Ligatures Properties
   *
   * Controls ligature rendering for enhanced typography.
   */
  --mico-font-variant-ligatures-common: common-ligatures;
  --mico-font-variant-ligatures-no-common: no-common-ligatures;
  --mico-font-variant-ligatures-discretionary: discretionary-ligatures;
  --mico-font-variant-ligatures-no-discretionary: no-discretionary-ligatures;
  --mico-font-variant-ligatures-historical: historical-ligatures;
  --mico-font-variant-ligatures-no-historical: no-historical-ligatures;
  --mico-font-variant-ligatures-contextual: contextual;
  --mico-font-variant-ligatures-no-contextual: no-contextual;

  /**
   * Font Variant Caps Properties
   *
   * Controls capitalization rendering for enhanced typography.
   */
  --mico-font-variant-caps-normal: var(--mico-value-normal);
  --mico-font-variant-caps-small-caps: small-caps;
  --mico-font-variant-caps-all-small-caps: all-small-caps;
  --mico-font-variant-caps-petite-caps: petite-caps;
  --mico-font-variant-caps-all-petite-caps: all-petite-caps;
  --mico-font-variant-caps-unicase: unicase;
  --mico-font-variant-caps-titling-caps: titling-caps;

  /**
   * Line Heights
   *
   * Line height controls the vertical spacing between lines of text.
   * - Values below 1.5 are good for headings
   * - Values 1.5-1.7 are ideal for body text for readability
   * - Larger values create more spacing for easier reading
   */
  --mico-lh-xs: 1;       /* Compact (headings) */
  --mico-lh-sm: 1.25;    /* Slightly compact */
  --mico-lh-md: 1.5;     /* Standard body text */
  --mico-lh-lg: 1.625;   /* Slightly relaxed */
  --mico-lh-xl: 2;       /* Loose (easy reading) */
  --mico-lh-2xl: 0.75rem; /* Fixed line height for small text */
  --mico-lh-3xl: 1rem;    /* Fixed line height */
  --mico-lh-4xl: 1.25rem; /* Fixed line height */
  --mico-lh-5xl: 1.5rem;  /* Fixed line height */
  --mico-lh-6xl: 1.75rem; /* Fixed line height */
  --mico-lh-7xl: 2rem;    /* Fixed line height */
  --mico-lh-8xl: 2.25rem; /* Fixed line height */
  --mico-lh-9xl: 2.5rem;  /* Fixed line height */


  /**
   * Letter Spacing
   *
   * Controls the horizontal spacing between characters.
   * - Negative values bring letters closer together
   * - Positive values spread letters apart
   * - 'em' units scale with the font size
   */
  --mico-ls-xs: -0.05em;   /* Tighter spacing */
  --mico-ls-sm: -0.025em;  /* Slightly tighter */
  --mico-ls-md: 0em;       /* Normal spacing */
  --mico-ls-lg: 0.025em;   /* Slightly wider */
  --mico-ls-xl: 0.05em;    /* Wider spacing */
  --mico-ls-2xl: 0.1em;    /* Widest spacing */

  /**
   * Text Decoration Properties
   *
   * Controls the appearance of underlines and other text decorations
   */
  --mico-underline-offset-auto: var(--mico-value-auto);
  --mico-underline-offset-0: var(--mico-value-0);
  --mico-underline-offset-1: 1px;
  --mico-underline-offset-2: 2px;
  --mico-underline-offset-4: 4px;
  --mico-underline-offset-8: 8px;
  --mico-underline-offset: 0.15em;  /* Default distance between text and underline */

  --mico-decoration-thickness-auto: var(--mico-value-auto);
  --mico-decoration-thickness-from-font: from-font;
  --mico-decoration-thickness-0: 0px;
  --mico-decoration-thickness-1: 1px;
  --mico-decoration-thickness-2: 2px;
  --mico-decoration-thickness-4: 4px;
  --mico-decoration-thickness-8: 8px;
  --mico-underline-thickness: 0.05em; /* Default thickness of the underline */

  --mico-decoration-style-solid: solid;
  --mico-decoration-style-double: double;
  --mico-decoration-style-dotted: dotted;
  --mico-decoration-style-dashed: dashed;
  --mico-decoration-style-wavy: wavy;

  --mico-underline-position-auto: var(--mico-value-auto);
  --mico-underline-position-under: under;
  --mico-underline-position-left: left;
  --mico-underline-position-right: right;

  /**
   * Text Transform Properties
   *
   * Controls text case transformation
   */
  --mico-text-transform-uppercase: uppercase;
  --mico-text-transform-lowercase: lowercase;
  --mico-text-transform-capitalize: capitalize;
  --mico-text-transform-none: var(--mico-value-none);

  /**
   * Text Alignment Properties
   *
   * These variables define text alignment values for consistent usage.
   */
  --mico-text-align-left: left;
  --mico-text-align-center: center;
  --mico-text-align-right: right;
  --mico-text-align-justify: justify;
  --mico-text-align-start: start;
  --mico-text-align-end: end;

  /**
   * Text Overflow Properties
   *
   * Controls how overflowing text is handled
   */
  --mico-text-overflow-ellipsis: ellipsis;
  --mico-text-overflow-clip: clip;

  /**
   * White Space Properties
   *
   * Controls how whitespace is handled
   */
  --mico-whitespace-normal: var(--mico-value-normal);
  --mico-whitespace-nowrap: nowrap;
  --mico-whitespace-pre: pre;
  --mico-whitespace-pre-line: pre-line;
  --mico-whitespace-pre-wrap: pre-wrap;
  --mico-whitespace-break-spaces: break-spaces;



  /**
   * Text Indent Properties
   *
   * Controls the indentation of the first line of text
   */
  --mico-indent-0: var(--mico-value-0);
  --mico-indent-xs: 1px;
  --mico-indent-sm: 0.25rem;
  --mico-indent-md: 0.5rem;
  --mico-indent-lg: 1rem;
  --mico-indent-xl: 2rem;

  /**
   * Text Shadow Properties
   *
   * Controls text shadow effects
   */
  --mico-text-shadow-none: var(--mico-value-none);
  --mico-text-shadow-xs: 1px 1px 2px rgba(0, 0, 0, 0.1);
  --mico-text-shadow-sm: 2px 2px 4px rgba(0, 0, 0, 0.1);
  --mico-text-shadow-md: 4px 4px 6px rgba(0, 0, 0, 0.1);
  --mico-text-shadow-lg: 6px 6px 8px rgba(0, 0, 0, 0.15);

  /**
   * Text Stroke Properties
   *
   * Controls text stroke (outline) effects
   */
  --mico-text-stroke-xs: 1px;
  --mico-text-stroke-sm: 2px;
  --mico-text-stroke-md: 4px;

  /**
   * List Style Properties
   *
   * Controls list styling
   */
  --mico-list-style-type-none: var(--mico-value-none);
  --mico-list-style-type-disc: disc;
  --mico-list-style-type-decimal: decimal;
  --mico-list-style-type-square: square;
  --mico-list-style-type-upper-roman: upper-roman;
  --mico-list-style-type-lower-roman: lower-roman;
  --mico-list-style-type-upper-alpha: upper-alpha;
  --mico-list-style-type-lower-alpha: lower-alpha;

  --mico-list-style-position-inside: inside;
  --mico-list-style-position-outside: outside;

  /**
   * Text Direction Properties
   *
   * Controls text direction for internationalization
   */
  --mico-text-direction-ltr: ltr;
  --mico-text-direction-rtl: rtl;

  /**
   * Writing Mode Properties
   *
   * Controls text writing direction
   */
  --mico-writing-mode-horizontal-tb: horizontal-tb;
  --mico-writing-mode-vertical-rl: vertical-rl;
  --mico-writing-mode-vertical-lr: vertical-lr;

  /**
   * Text Orientation Properties
   *
   * Controls text orientation in vertical writing modes
   */
  --mico-text-orientation-mixed: mixed;
  --mico-text-orientation-upright: upright;
  --mico-text-orientation-sideways: sideways;

  /**
   * Hyphens Properties
   *
   * Controls automatic hyphenation
   */
  --mico-hyphens-none: var(--mico-value-none);
  --mico-hyphens-manual: manual;
  --mico-hyphens-auto: var(--mico-value-auto);

  /**
   * Text Align Last Properties
   *
   * Controls alignment of the last line in a block
   */
  --mico-text-align-last-auto: var(--mico-value-auto);
  --mico-text-align-last-start: start;
  --mico-text-align-last-end: end;
  --mico-text-align-last-left: left;
  --mico-text-align-last-right: right;
  --mico-text-align-last-center: center;
  --mico-text-align-last-justify: justify;

  /**
   * Text Justify Properties
   *
   * Controls how justified text is spaced
   */
  --mico-text-justify-auto: var(--mico-value-auto);
  --mico-text-justify-inter-word: inter-word;
  --mico-text-justify-inter-character: inter-character;
  --mico-text-justify-none: var(--mico-value-none);

  /**
   * User Select Properties
   *
   * Controls whether text can be selected
   */
  --mico-user-select-none: var(--mico-value-none);
  --mico-user-select-text: text;
  --mico-user-select-all: all;
  --mico-user-select-auto: var(--mico-value-auto);

  /**
   * Word Break Properties
   *
   * Controls how words break at line endings
   */
  --mico-word-break-normal: var(--mico-value-normal);
  --mico-word-break-break-all: break-all;
  --mico-word-break-keep-all: keep-all;
  --mico-overflow-wrap-normal: var(--mico-value-normal);
  --mico-overflow-wrap-break-word: break-word;

  /* ====================================================================== */
  /* SPACING SYSTEM                                                         */
  /* ====================================================================== */

  /**
   * Spacing Scale
   *
   * A comprehensive spacing system based on a 4px unit.
   * This creates a consistent rhythm throughout the interface.
   *
   * The base unit (--mico-size-unit) is 4px, and all other spacing
   * values are multiples of this unit, making it easy to maintain
   * consistent proportional spacing.
   */
  --mico-size-unit: 4px;            /* Base unit for spacing system */

  /* Core spacing values (most commonly used) */
  --mico-size-0: 0;                 /* No spacing */
  --mico-size-1: 1px;               /* Pixel-perfect adjustments */
  --mico-size-2: 2px;               /* Minimal spacing */
  --mico-size-3: 3px;               /* Minimal spacing */
  --mico-size-4: calc(var(--mico-size-unit) * 1);    /* 4px - Tiny spacing */
  --mico-size-6: 6px;               /* Small spacing */
  --mico-size-8: calc(var(--mico-size-unit) * 2);    /* 8px - Extra small spacing */
  --mico-size-10: 10px;             /* Small spacing */
  --mico-size-12: calc(var(--mico-size-unit) * 3);   /* 12px - Small spacing */
  --mico-size-14: 14px;             /* Small spacing */
  --mico-size-16: calc(var(--mico-size-unit) * 4);   /* 16px - Default spacing */
  --mico-size-18: 18px;             /* Medium spacing */
  --mico-size-20: calc(var(--mico-size-unit) * 5);   /* 20px - Medium spacing */
  --mico-size-22: 22px;             /* Medium spacing */
  --mico-size-24: calc(var(--mico-size-unit) * 6);   /* 24px - Medium spacing */
  --mico-size-28: calc(var(--mico-size-unit) * 7);   /* 28px - Medium spacing */
  --mico-size-32: calc(var(--mico-size-unit) * 8);   /* 32px - Large spacing */
  --mico-size-36: calc(var(--mico-size-unit) * 9);   /* 36px - Large spacing */
  --mico-size-40: calc(var(--mico-size-unit) * 10);  /* 40px - Extra large spacing */
  --mico-size-44: calc(var(--mico-size-unit) * 11);  /* 44px - Extra large spacing */
  --mico-size-48: calc(var(--mico-size-unit) * 12);  /* 48px - Extra large spacing */
  --mico-size-52: calc(var(--mico-size-unit) * 13);  /* 52px - Extra large spacing */
  --mico-size-56: calc(var(--mico-size-unit) * 14);  /* 56px - Huge spacing */
  --mico-size-60: calc(var(--mico-size-unit) * 15);  /* 60px - Huge spacing */
  --mico-size-64: calc(var(--mico-size-unit) * 16);  /* 64px - Huge spacing */
  --mico-size-72: calc(var(--mico-size-unit) * 18);  /* 72px - Huge spacing */
  --mico-size-80: calc(var(--mico-size-unit) * 20);  /* 80px - Huge spacing */
  --mico-size-96: calc(var(--mico-size-unit) * 24);  /* 96px - Giant spacing */
  --mico-size-100: calc(var(--mico-size-unit) * 25); /* 100px - Giant spacing */
  --mico-size-112: calc(var(--mico-size-unit) * 28); /* 112px - Section spacing */
  --mico-size-128: calc(var(--mico-size-unit) * 32); /* 128px - Section spacing */
  --mico-size-144: calc(var(--mico-size-unit) * 36); /* 144px - Large section spacing */
  --mico-size-160: calc(var(--mico-size-unit) * 40); /* 160px - Large section spacing */
  --mico-size-176: calc(var(--mico-size-unit) * 44); /* 176px - Extra large section spacing */
  --mico-size-192: calc(var(--mico-size-unit) * 48); /* 192px - Extra large section spacing */
  --mico-size-208: calc(var(--mico-size-unit) * 52); /* 208px - Huge section spacing */
  --mico-size-224: calc(var(--mico-size-unit) * 56); /* 224px - Huge section spacing */
  --mico-size-240: calc(var(--mico-size-unit) * 60); /* 240px - Maximum spacing */
  --mico-size-256: calc(var(--mico-size-unit) * 64); /* 256px - Maximum spacing */


  /**
   * Main & Section Fluid Spacing Gutter
   *
   * Responsive spacing that adapts to viewport size.
   * Uses clamp() to create spacing that scales between a minimum and maximum value.
   *
   * Format: clamp(min-size, viewport-based-size, max-size)
   */
  --mico-size-fluid-xs: clamp(var(--mico-size-16), 3vw, var(--mico-size-32));
  --mico-size-fluid-sm: clamp(var(--mico-size-16), 4vw, var(--mico-size-56));
  --mico-size-fluid-md: clamp(var(--mico-size-16), 6vw, var(--mico-size-80));
  --mico-size-fluid-lg: clamp(var(--mico-size-16), 8vw, var(--mico-size-100));
  --mico-size-fluid-xl: clamp(var(--mico-size-16), 10vw, var(--mico-size-128));
  --mico-size-fluid-2xl: clamp(var(--mico-size-16), 12vw, var(--mico-size-192));


  /* ====================================================================== */
  /* BORDER SYSTEM                                                          */
  /* ====================================================================== */

  /**
   * Border Radius
   *
   * Controls the roundness of element corners.
   * Consistent border radius values create a cohesive design language.
   */
  --mico-radius-none: 0;            /* No rounding */
  --mico-radius-xs: 1px;            /* Barely visible rounding */
  --mico-radius-sm: 2px;            /* Subtle rounding */
  --mico-radius-md: 4px;            /* Standard rounding */
  --mico-radius-lg: 8px;            /* Prominent rounding */
  --mico-radius-xl: 12px;           /* Very rounded corners */
  --mico-radius-2xl: 16px;          /* Extra rounded corners */
  --mico-radius-full: 9999px;       /* Fully rounded (circles/pills) */

  /**
   * Border Styles
   *
   * Standard CSS border styles for consistent usage.
   * These variables make it easier to maintain consistent border styles.
   */
  --mico-border-none: none;
  --mico-border-solid: solid;
  --mico-border-dashed: dashed;
  --mico-border-dotted: dotted;
  --mico-border-double: double;
  --mico-border-groove: groove;
  --mico-border-ridge: ridge;
  --mico-border-inset: inset;
  --mico-border-outset: outset;

  /**
   * Border Widths
   *
   * Standard border thickness values.
   * These follow the same scale pattern as other properties.
   */
  --mico-border-width-0: 0px;       /* No border */
  --mico-border-width-1: 1px;       /* Thin border (standard) */
  --mico-border-width-2: 2px;       /* Medium border */
  --mico-border-width-4: 4px;       /* Thick border */
  --mico-border-width-8: 8px;       /* Very thick border */

  /**
   * Outline Properties
   *
   * These variables define outline styles for accessibility and focus states.
   */
  --mico-outline-width-0: 0px;      /* No outline */
  --mico-outline-width-1: 1px;      /* Thin outline */
  --mico-outline-width-2: 2px;      /* Medium outline */
  --mico-outline-width-4: 4px;      /* Thick outline */
  --mico-outline-width-8: 8px;      /* Very thick outline */

  --mico-outline-style-none: none;  /* No outline */
  --mico-outline-style-solid: solid; /* Solid outline */
  --mico-outline-style-dashed: dashed; /* Dashed outline */
  --mico-outline-style-dotted: dotted; /* Dotted outline */
  --mico-outline-style-double: double; /* Double outline */

  --mico-outline-offset-0: 0px;     /* No offset */
  --mico-outline-offset-1: 1px;     /* Small offset */
  --mico-outline-offset-2: 2px;     /* Medium offset */
  --mico-outline-offset-4: 4px;     /* Large offset */
  --mico-outline-offset-8: 8px;     /* Extra large offset */

  /**
   * Ring Properties (Box Shadow Based)
   *
   * These variables define ring effects using box-shadow for flexible styling.
   */
  --mico-ring-width-0: 0px;         /* No ring */
  --mico-ring-width-1: 1px;         /* Thin ring */
  --mico-ring-width-2: 2px;         /* Medium ring */
  --mico-ring-width-4: 4px;         /* Thick ring */
  --mico-ring-width-8: 8px;         /* Very thick ring */

  --mico-ring-offset-width-0: 0px;  /* No ring offset */
  --mico-ring-offset-width-1: 1px;  /* Small ring offset */
  --mico-ring-offset-width-2: 2px;  /* Medium ring offset */
  --mico-ring-offset-width-4: 4px;  /* Large ring offset */
  --mico-ring-offset-width-8: 8px;  /* Extra large ring offset */

  /**
   * Divide Properties
   *
   * These variables define border styles for dividing child elements.
   */
  --mico-divide-width-0: 0px;       /* No divide border */
  --mico-divide-width-1: 1px;       /* Thin divide border */
  --mico-divide-width-2: 2px;       /* Medium divide border */
  --mico-divide-width-4: 4px;       /* Thick divide border */
  --mico-divide-width-8: 8px;       /* Very thick divide border */

  /* ====================================================================== */
  /* SHADOW SYSTEM                                                          */
  /* ====================================================================== */

  /**
   * Box Shadows
   *
   * Creates depth and elevation in the interface.
   * Different shadow intensities represent different elevation levels.
   *
   * Light mode shadows use black with opacity for a subtle effect.
   * Dark mode shadows use white with opacity for a subtle effect.
   */

  /* Light Mode Shadows */
  --mico-shadow-sm-light: 0 1px 2px rgba(0, 0, 0, 0.05);  /* Subtle shadow */
  --mico-shadow-md-light: 0 4px 6px rgba(0, 0, 0, 0.1);   /* Medium shadow */
  --mico-shadow-lg-light: 0 10px 15px rgba(0, 0, 0, 0.1); /* Large shadow */
  --mico-shadow-xl-light: 0 20px 25px rgba(0, 0, 0, 0.15); /* Extra large shadow */

  /* Dark Mode Shadows */
  --mico-shadow-sm-dark: 0 1px 2px rgba(255, 255, 255, 0.05);
  --mico-shadow-md-dark: 0 4px 6px rgba(255, 255, 255, 0.1);
  --mico-shadow-lg-dark: 0 10px 15px rgba(255, 255, 255, 0.1);
  --mico-shadow-xl-dark: 0 20px 25px rgba(255, 255, 255, 0.15);

  /* Default shadows (will be overridden in dark mode) */
  --mico-shadow-sm: var(--mico-shadow-sm-light);
  --mico-shadow-md: var(--mico-shadow-md-light);
  --mico-shadow-lg: var(--mico-shadow-lg-light);
  --mico-shadow-xl: var(--mico-shadow-xl-light);

  /* Inset Shadows (for pressed/inset effects) */
  --mico-shadow-inset-sm: inset 0 1px 2px rgba(0, 0, 0, 0.05);
  --mico-shadow-inset-md: inset 0 4px 6px rgba(0, 0, 0, 0.1);

  /* Focus Shadow for accessibility */
  --mico-shadow-focus: 0 0 0 3px rgba(66, 153, 225, 0.5);

  /**
   * Media Queries for Adaptive Shadows
   *
   * These media queries automatically adjust shadow styles based on user preferences.
   * - Dark mode: Uses lighter shadows on dark backgrounds
   * - High contrast mode: Uses more visible focus indicators
   */
  @media (prefers-color-scheme: dark) {
    --mico-shadow-sm: var(--mico-shadow-sm-dark);
    --mico-shadow-md: var(--mico-shadow-md-dark);
    --mico-shadow-lg: var(--mico-shadow-lg-dark);
    --mico-shadow-xl: var(--mico-shadow-xl-dark);
    --mico-shadow-inset-sm: inset 0 1px 2px rgba(255, 255, 255, 0.05);
    --mico-shadow-inset-md: inset 0 4px 6px rgba(255, 255, 255, 0.1);
    --mico-shadow-focus: 0 0 0 3px rgba(191, 219, 254, 0.6);
  }

  @media (prefers-contrast: high) {
    --mico-shadow-focus: 0 0 0 4px rgba(0, 0, 0, 1);
    --mico-shadow-sm: 0 0 0 1px currentColor;
    --mico-shadow-md: 0 0 0 2px currentColor;
    --mico-shadow-lg: 0 0 0 3px currentColor;
    --mico-shadow-xl: 0 0 0 4px currentColor;
  }

  /* ====================================================================== */
  /* LAYOUT SYSTEM                                                          */
  /* ====================================================================== */

  /**
   * Layout Properties
   *
   * These variables define common layout values for consistent usage.
   * Using variables for these properties ensures consistency across the codebase.
   */

  /**
   * Position Properties
   *
   * These variables define CSS position values for consistent usage.
   * Position determines how an element is positioned in the document flow.
   */
  --mico-position-static: static;     /* Default positioning in document flow */
  --mico-position-relative: relative; /* Positioned relative to normal position */
  --mico-position-absolute: absolute; /* Positioned relative to nearest positioned ancestor */
  --mico-position-fixed: fixed;       /* Positioned relative to viewport */
  --mico-position-sticky: sticky;     /* Positioned based on scroll position */

  /**
   * Display Properties
   *
   * These variables define CSS display values for consistent usage.
   * Display determines how an element is rendered in the layout.
   */
  --mico-display-block: block;           /* Element generates a block box */
  --mico-display-inline: inline;         /* Element generates an inline box */
  --mico-display-inline-block: inline-block; /* Inline-level block container */
  --mico-display-flex: flex;             /* Flexible box layout */
  --mico-display-inline-flex: inline-flex; /* Inline-level flex container */
  --mico-display-grid: grid;             /* Grid layout */
  --mico-display-none: none;             /* Element is not displayed */

  /**
   * Box Model Properties
   *
   * These variables define box model behavior for consistent usage.
   */
  --mico-box-sizing-border: border-box;  /* Width/height includes padding and border */
  --mico-box-sizing-content: content-box; /* Width/height excludes padding and border */
  --mico-box-decoration-slice: slice;    /* Background doesn't extend across fragments */
  --mico-box-decoration-clone: clone;    /* Background extends across fragments */

  /**
   * Overflow Properties
   *
   * These variables define how content that overflows the element's box is handled.
   */
  --mico-overflow-auto: auto;           /* Add scrollbars when needed */
  --mico-overflow-hidden: hidden;       /* Clip overflow content */
  --mico-overflow-visible: visible;     /* Content not clipped, may overflow */
  --mico-overflow-scroll: scroll;       /* Always show scrollbars */
  --mico-overscroll-auto: auto;         /* Default overscroll behavior */
  --mico-overscroll-contain: contain;   /* Prevent scroll chaining */
  --mico-overscroll-none: none;         /* Prevent overscroll effects */

  /**
   * Aspect Ratio Properties
   *
   * These variables define common aspect ratios for responsive elements.
   * Useful for maintaining proportional dimensions for images, videos, etc.
   */
  --mico-aspect-ratio-square: 1 / 1;        /* 1:1 ratio (square) */
  --mico-aspect-ratio-video: 16 / 9;        /* 16:9 ratio (standard video) */
  --mico-aspect-ratio-portrait: 3 / 4;      /* 3:4 ratio (portrait) */
  --mico-aspect-ratio-landscape: 4 / 3;     /* 4:3 ratio (landscape) */
  --mico-aspect-ratio-widescreen: 21 / 9;   /* 21:9 ratio (ultrawide) */
  --mico-aspect-ratio-golden: 1.618 / 1;    /* Golden ratio (aesthetically pleasing) */

  /**
   * Float and Clear Properties
   *
   * These variables define float and clear values for consistent usage.
   * Float allows elements to be placed to the left or right of their container.
   */
  --mico-float-left: left;              /* Float element to the left */
  --mico-float-right: right;            /* Float element to the right */
  --mico-float-none: none;              /* Do not float element */
  --mico-clear-left: left;              /* Clear left floats */
  --mico-clear-right: right;            /* Clear right floats */
  --mico-clear-both: both;              /* Clear both left and right floats */

  /**
   * Object Fit Properties
   *
   * These variables define how replaced elements (like images) should be resized.
   */
  --mico-object-fit-contain: contain;    /* Preserve aspect ratio, fit within box */
  --mico-object-fit-cover: cover;        /* Fill box, may crop image */
  --mico-object-fit-fill: fill;          /* Stretch to fill box */
  --mico-object-fit-scale-down: scale-down; /* Smaller of contain or none */
  --mico-object-position-center: center; /* Center the object within its box */

  /**
   * Visibility and Isolation Properties
   *
   * These variables define visibility and stacking context behavior.
   */
  --mico-visibility-visible: visible;    /* Element is visible */
  --mico-visibility-hidden: hidden;      /* Element is hidden but takes up space */
  --mico-visibility-collapse: collapse;  /* Element is hidden (for table rows/columns) */
  --mico-isolation-isolate: isolate;     /* Create new stacking context */
  --mico-isolation-auto: auto;           /* Default stacking context behavior */

  /**
   * Positioning Properties
   *
   * These variables define common inset values for positioned elements.
   */
  --mico-inset-0: 0;                     /* No offset from container edges */
  --mico-inset-auto: auto;               /* Automatic positioning */

  /* ====================================================================== */
  /* FLEXBOX SYSTEM                                                          */
  /* ====================================================================== */

  /**
   * Flexbox Properties
   *
   * These variables define common flexbox values for consistent usage.
   * Flexbox is a one-dimensional layout method for arranging items.
   */

  /* Flex Direction */
  --mico-flex-row: row;               /* Items arranged in a row */
  --mico-flex-row-reverse: row-reverse; /* Items arranged in a row, reversed */
  --mico-flex-col: column;            /* Items arranged in a column */
  --mico-flex-col-reverse: column-reverse; /* Items arranged in a column, reversed */

  /* Flex Wrap */
  --mico-flex-wrap: wrap;             /* Items wrap to multiple lines */
  --mico-flex-nowrap: nowrap;         /* Items forced into a single line */
  --mico-flex-wrap-reverse: wrap-reverse; /* Items wrap to multiple lines, reversed */

  /* Justify Content (main axis) */
  --mico-justify-start: flex-start;   /* Items packed at start of container */
  --mico-justify-end: flex-end;       /* Items packed at end of container */
  --mico-justify-center: center;      /* Items centered in container */
  --mico-justify-between: space-between; /* Items evenly distributed with space between */
  --mico-justify-around: space-around; /* Items evenly distributed with space around */
  --mico-justify-evenly: space-evenly; /* Items evenly distributed with equal space */

  /* Align Items (cross axis) */
  --mico-items-start: flex-start;     /* Items aligned at start of cross axis */
  --mico-items-end: flex-end;         /* Items aligned at end of cross axis */
  --mico-items-center: center;        /* Items centered on cross axis */
  --mico-items-baseline: baseline;    /* Items aligned by text baseline */
  --mico-items-stretch: stretch;      /* Items stretched to fill container */

  /* ====================================================================== */
  /* GRID SYSTEM                                                            */
  /* ====================================================================== */
  /**
   * Grid Auto Properties
   *
   * These variables define how grid items are automatically placed and sized.
   * Auto-fit and auto-fill create responsive layouts without media queries.
   */
  --mico-grid-auto-fit: auto-fit;    /* Expands items to fill available space */
  --mico-grid-auto-fill: auto-fill;  /* Creates as many tracks as possible */

  /**
   * Grid Placement Properties
   *
   * These variables define how grid items are placed within the grid container.
   * They control both individual items and groups of items.
   */
  --mico-place-items-start: start;    /* Items placed at start of their area */
  --mico-place-items-end: end;        /* Items placed at end of their area */
  --mico-place-items-center: center;  /* Items placed at center of their area */
  --mico-place-items-stretch: stretch; /* Items stretched to fill their area */

  --mico-place-content-start: start;    /* Content placed at start of grid */
  --mico-place-content-end: end;        /* Content placed at end of grid */
  --mico-place-content-center: center;  /* Content placed at center of grid */
  --mico-place-content-stretch: stretch; /* Content stretched to fill grid */
  --mico-place-content-around: space-around; /* Content evenly distributed with space around */
  --mico-place-content-between: space-between; /* Content evenly distributed with space between */
  --mico-place-content-evenly: space-evenly;   /* Content evenly distributed with equal space */

  /**
   * Grid Properties
   *
   * These variables define common grid layout values for consistent usage.
   * CSS Grid is a two-dimensional layout system for complex layouts.
   */

  /* Grid Configuration */
  --mico-grid-column-count: 12;       /* Default number of columns */
  --mico-grid-min-column-width: 200px; /* Minimum column width for responsive grids */
  --mico-grid-row-count: 1;           /* Default number of rows */
  --mico-grid-min-row-height: 100px;  /* Minimum row height for responsive grids */

  /* Grid Item Placement */
  --mico-column-span: 1;              /* Default column span for grid items */
  --mico-row-span: 1;                 /* Default row span for grid items */
  --mico-min-column-width: 0;         /* Minimum width for auto columns */
  --mico-max-column-width: 1fr;       /* Maximum width for auto columns */
  --mico-min-row-height: 0;           /* Minimum height for auto rows */
  --mico-max-row-height: 1fr;         /* Maximum height for auto rows */

  /* Grid Templates */
  --mico-grid-cols: repeat(var(--mico-grid-column-count, 12), minmax(0, 1fr)); /* Equal columns */
  --mico-grid-cols-auto-fit: repeat(auto-fit, minmax(var(--mico-grid-min-column-width, 200px), 1fr)); /* Responsive columns */
  --mico-grid-rows: repeat(var(--mico-grid-row-count, 1), minmax(0, 1fr)); /* Equal rows */

  /* Grid Item Placement */
  --mico-col-span: span var(--mico-column-span, 1); /* Column span for grid items */
  --mico-row-span: span var(--mico-row-span, 1);    /* Row span for grid items */

  /* Grid Flow */
  --mico-grid-flow-row: row;          /* Grid auto-placement by row */
  --mico-grid-flow-col: column;       /* Grid auto-placement by column */
  --mico-grid-flow-dense: dense;      /* Dense packing algorithm */

  /* Grid Auto Columns and Rows */
  --mico-auto-cols: minmax(var(--mico-min-column-width, 0), var(--mico-max-column-width, 1fr));
  --mico-auto-rows: minmax(var(--mico-min-row-height, 0), var(--mico-max-row-height, 1fr));

  /* Grid Gap */
  --mico-gap-xs: var(--mico-size-4);  /* Extra small gap */
  --mico-gap-sm: var(--mico-size-8);  /* Small gap */
  --mico-gap-md: var(--mico-size-16); /* Medium gap (default) */
  --mico-gap-lg: var(--mico-size-24); /* Large gap */
  --mico-gap-xl: var(--mico-size-32); /* Extra large gap */

  --mico-gap: var(--mico-gap-md);     /* Default gap */
  --mico-row-gap: var(--mico-gap);    /* Gap between rows */
  --mico-column-gap: var(--mico-gap); /* Gap between columns */

  /* ====================================================================== */
  /* BACKGROUND SYSTEM                                                       */
  /* ====================================================================== */

  /**
   * Background Properties
   *
   * These variables define common background property values for consistent usage.
   * Background properties control how backgrounds are displayed.
   */

  /* Background Repeat */
  --mico-bg-none: none;               /* No background image */
  --mico-bg-repeat: repeat;           /* Repeat in both directions */
  --mico-bg-no-repeat: no-repeat;     /* No repetition */
  --mico-bg-repeat-x: repeat-x;       /* Repeat horizontally only */
  --mico-bg-repeat-y: repeat-y;       /* Repeat vertically only */

  /* Background Attachment */
  --mico-bg-fixed: fixed;             /* Fixed to viewport */
  --mico-bg-local: local;             /* Scrolls with content */
  --mico-bg-scroll: scroll;           /* Scrolls with element */

  /* Background Clip */
  --mico-bg-clip-border: border-box;  /* Extend to outer border edge */
  --mico-bg-clip-padding: padding-box; /* Extend to outer padding edge */
  --mico-bg-clip-content: content-box; /* Extend to content edge */

  /* ====================================================================== */
  /* FILTER SYSTEM                                                          */
  /* ====================================================================== */

  /**
   * Filter Properties
   *
   * These variables define CSS filter effects for visual manipulation.
   * Filters can be combined to create complex visual effects.
   */
  --mico-filter-blur: blur(8px);           /* Blurs the element */
  --mico-filter-brightness: brightness(1.5); /* Adjusts brightness */
  --mico-filter-contrast: contrast(1.2);    /* Adjusts contrast */
  --mico-filter-grayscale: grayscale(100%); /* Converts to grayscale */
  --mico-filter-hue-rotate: hue-rotate(90deg); /* Shifts colors */
  --mico-filter-invert: invert(100%);      /* Inverts colors */
  --mico-filter-saturate: saturate(2);     /* Adjusts color saturation */
  --mico-filter-sepia: sepia(100%);        /* Applies sepia tone */

  /* ====================================================================== */
  /* OPACITY SYSTEM                                                          */
  /* ====================================================================== */

  /**
   * Opacity Properties
   *
   * These variables define standard opacity values for consistent usage.
   * Opacity controls the transparency of elements.
   */
  --mico-opacity-0: 0;                /* Completely transparent */
  --mico-opacity-25: 0.25;            /* Mostly transparent */
  --mico-opacity-50: 0.5;             /* Semi-transparent */
  --mico-opacity-75: 0.75;            /* Slightly transparent */
  --mico-opacity-100: 1;              /* Completely opaque */

  /* ====================================================================== */
  /* TRANSFORM SYSTEM                                                       */
  /* ====================================================================== */

  /**
   * Transform Properties
   *
   * These variables define common transform functions for consistent usage.
   * Transforms allow elements to be visually manipulated in 2D or 3D space.
   */

  /* Scale Transforms */
  --mico-scale-100: scale(1);         /* Original size (no scaling) */
  --mico-scale-75: scale(0.75);       /* 75% of original size */
  --mico-scale-50: scale(0.5);        /* 50% of original size */

  /* Rotation Transforms */
  --mico-rotate-45: rotate(45deg);    /* 45-degree rotation */
  --mico-rotate-90: rotate(90deg);    /* 90-degree rotation */

  /* Translation Transforms */
  --mico-translate-x-full: translateX(100%); /* Move 100% right */
  --mico-translate-y-full: translateY(100%); /* Move 100% down */

  /* ====================================================================== */
  /* TABLE SYSTEM                                                           */
  /* ====================================================================== */

  /**
   * Table Properties
   *
   * These variables define table layout algorithms for consistent usage.
   * Table layout affects how tables calculate column widths.
   */
  --mico-table-auto: auto;            /* Automatic table layout algorithm */
  --mico-table-fixed: fixed;          /* Fixed table layout algorithm */

  /* ====================================================================== */
  /* SVG SYSTEM                                                             */
  /* ====================================================================== */

  /**
   * SVG Properties
   *
   * These variables define common SVG property values for consistent usage.
   * These help maintain consistent styling between HTML and SVG elements.
   */
  --mico-fill-current: currentColor;  /* Use current text color for fill */
  --mico-stroke-current: currentColor; /* Use current text color for stroke */

  /* ====================================================================== */
  /* BUTTON SYSTEM                                                          */
  /* ====================================================================== */

  /**
   * Button Size Variables
   *
   * These variables define consistent button sizing across the framework.
   */
  --mico-btn-padding-xs: var(--mico-size-8) var(--mico-size-12);       /* Extra small button padding */
  --mico-btn-padding-sm: var(--mico-size-12) var(--mico-size-16);      /* Small button padding */
  --mico-btn-padding-md: var(--mico-size-16) var(--mico-size-24);      /* Medium button padding (default) */
  --mico-btn-padding-lg: var(--mico-size-20) var(--mico-size-32);      /* Large button padding */
  --mico-btn-padding-xl: var(--mico-size-24) var(--mico-size-40);      /* Extra large button padding */

  --mico-btn-font-size-xs: var(--mico-fs-xs);                          /* Extra small button font size */
  --mico-btn-font-size-sm: var(--mico-fs-sm);                          /* Small button font size */
  --mico-btn-font-size-md: var(--mico-fs-md);                          /* Medium button font size (default) */
  --mico-btn-font-size-lg: var(--mico-fs-lg);                          /* Large button font size */
  --mico-btn-font-size-xl: var(--mico-fs-xl);                          /* Extra large button font size */

  /**
   * Button Border Radius Variables
   *
   * These variables define button border radius options.
   */
  --mico-btn-radius-square: var(--mico-radius-none);                    /* Square buttons (no radius) */
  --mico-btn-radius-sm: var(--mico-radius-sm);                         /* Small radius */
  --mico-btn-radius-md: var(--mico-radius-md);                         /* Medium radius (default) */
  --mico-btn-radius-lg: var(--mico-radius-lg);                         /* Large radius */
  --mico-btn-radius-pill: var(--mico-radius-full);                     /* Pill-shaped buttons */
  --mico-btn-radius-circle: var(--mico-radius-full);                   /* Circular buttons */

  /**
   * Button Shadow Variables
   *
   * These variables define button shadow options for depth and elevation.
   */
  --mico-btn-shadow-none: none;                                         /* No shadow */
  --mico-btn-shadow-sm: var(--mico-shadow-sm);                         /* Small shadow */
  --mico-btn-shadow-md: var(--mico-shadow-md);                         /* Medium shadow (default) */
  --mico-btn-shadow-lg: var(--mico-shadow-lg);                         /* Large shadow */

  /**
   * Button Icon Variables
   *
   * These variables define spacing and sizing for buttons with icons.
   */
  --mico-btn-icon-gap: var(--mico-size-8);                             /* Gap between icon and text */
  --mico-btn-icon-only-size: var(--mico-size-40);                      /* Size for icon-only buttons */

  /* ====================================================================== */
  /* CURSOR SYSTEM                                                          */
  /* ====================================================================== */

  /**
   * Cursor Properties
   *
   * These variables define cursor styles for different interactive states.
   * Cursors provide visual feedback about what the user can do.
   */
  --mico-cursor-auto: auto;           /* Browser default cursor */
  --mico-cursor-default: default;     /* Default cursor (arrow) */
  --mico-cursor-pointer: pointer;     /* Pointing hand (for links) */
  --mico-cursor-wait: wait;           /* Waiting (hourglass) */
  --mico-cursor-text: text;           /* Text selection (I-beam) */
  --mico-cursor-move: move;           /* Movement indicator */
  --mico-cursor-not-allowed: not-allowed; /* Forbidden action */
  --mico-cursor-grab: grab;           /* Grabbable element */
  --mico-cursor-grabbing: grabbing;   /* Element being grabbed */
  --mico-cursor-help: help;           /* Help cursor */

  /* ====================================================================== */
  /* MISCELLANEOUS UTILITIES SYSTEM                                         */
  /* ====================================================================== */

  /**
   * Appearance Variables
   *
   * These variables control the appearance property for form elements.
   */
  --mico-appearance-none: none;       /* Remove default browser styling */
  --mico-appearance-auto: auto;       /* Use default browser styling */

  /**
   * Pointer Events Variables
   *
   * These variables control pointer event behavior.
   */
  --mico-pointer-events-none: none;   /* Element cannot be target of pointer events */
  --mico-pointer-events-auto: auto;   /* Element can be target of pointer events */

  /**
   * Will Change Variables
   *
   * These variables provide performance hints to the browser.
   * Use with caution as they can consume more resources if overused.
   */
  --mico-will-change-auto: auto;      /* Browser decides what to optimize */
  --mico-will-change-transform: transform; /* Optimize for transform changes */
  --mico-will-change-opacity: opacity; /* Optimize for opacity changes */
  --mico-will-change-scroll: scroll-position; /* Optimize for scroll changes */

  /**
   * Bleed Utility Variables
   *
   * These variables control full-bleed and column-bleed effects.
   */
  --mico-bleed-offset-sm: 10vw;       /* Small bleed offset */
  --mico-bleed-offset-md: 20vw;       /* Medium bleed offset (default) */
  --mico-bleed-offset-lg: 30vw;       /* Large bleed offset */

  /**
   * Mask Variables
   *
   * These variables define CSS mask gradients for fade effects.
   */
  --mico-mask-fade-to-top: linear-gradient(to top, black 50%, transparent 100%);
  --mico-mask-fade-to-bottom: linear-gradient(to bottom, black 50%, transparent 100%);
  --mico-mask-fade-to-left: linear-gradient(to left, black 50%, transparent 100%);
  --mico-mask-fade-to-right: linear-gradient(to right, black 50%, transparent 100%);

  /* Fade intensity variations */
  --mico-mask-fade-short: linear-gradient(to bottom, black 80%, transparent 100%);
  --mico-mask-fade-long: linear-gradient(to bottom, black 20%, transparent 100%);

  /* ====================================================================== */
  /* SIZING SYSTEM                                                          */
  /* ====================================================================== */

  /**
   * Width and Height Properties
   *
   * These variables define common sizing values for consistent usage.
   * Consistent sizing helps maintain a cohesive layout.
   */

  /* Content-based Sizing */
  --mico-fit-content: fit-content;    /* Size based on content with constraints */
  --mico-min-content: min-content;    /* Smallest size that fits content */
  --mico-max-content: max-content;    /* Largest size needed for content */

  /* Percentage-based Sizing */
  --mico-width-full: 100%;            /* Full width of container */
  --mico-height-full: 100%;           /* Full height of container */
  --mico-width-half: 50%;             /* Half width of container */
  --mico-height-half: 50%;            /* Half height of container */
  --mico-width-quarter: 25%;           /* Quarter width of container */
  --mico-height-quarter: 25%;         /* Quarter height of container */
  --mico-width-third: 33.33%;         /* Third width of container */
  --mico-height-third: 33.33%;       /* Third height of container */

  /* Viewport-based Sizing */
  --mico-width-screen: 100vw;         /* Full viewport width */
  --mico-height-screen: 100vh;        /* Full viewport height */

  /* Min/Max Constraints */
  --mico-min-width-0: 0;              /* No minimum width */
  --mico-min-height-0: 0;             /* No minimum height */
  --mico-max-width-full: 100%;        /* Maximum width of container */
  --mico-max-height-full: 100%;       /* Maximum height of container */

  /* ====================================================================== */
  /* ALIGNMENT SYSTEM                                                       */
  /* ====================================================================== */

  /**
   * Vertical Alignment Properties
   *
   * These variables define vertical alignment values for consistent usage.
   * Vertical alignment controls how inline or table-cell elements are aligned.
   */

  /* Basic Vertical Alignment */
  --mico-align-baseline: baseline;    /* Align to text baseline */
  --mico-align-top: top;              /* Align to top */
  --mico-align-middle: middle;        /* Align to middle */
  --mico-align-bottom: bottom;        /* Align to bottom */
  --mico-align-text-top: text-top;    /* Align to top of text */
  --mico-align-text-bottom: text-bottom; /* Align to bottom of text */

  /* Extended Vertical Alignment */
  --mico-vertical-align-sub: sub;     /* Subscript alignment */
  --mico-vertical-align-super: super; /* Superscript alignment */

  /* ====================================================================== */
  /* ANIMATION SYSTEM                                                       */
  /* ====================================================================== */

  /**
   * Animation Duration Variables
   *
   * These variables define animation durations for consistent timing across the framework.
   * Used by both transitions and animations for unified motion design.
   */
  --mico-duration-xs: 0.2s;          /* Extra fast animations */
  --mico-duration-sm: 0.5s;          /* Fast animations */
  --mico-duration-md: 0.8s;          /* Standard animations */
  --mico-duration-lg: 1.2s;          /* Slow animations */
  --mico-duration-xl: 2s;            /* Very slow animations */

  /**
   * Animation Delay Variables
   *
   * These variables define animation delays for staggered effects.
   */
  --mico-delay-xs: 0.1s;             /* Minimal delay */
  --mico-delay-sm: 0.2s;             /* Small delay */
  --mico-delay-md: 0.4s;             /* Medium delay */
  --mico-delay-lg: 0.6s;             /* Large delay */
  --mico-delay-xl: 0.8s;             /* Extra large delay */

  /**
   * Animation Engine Variables
   *
   * These variables control the default behavior of the animation engine.
   */
  --mico-anim-default-duration: var(--mico-duration-md);        /* Default animation duration */
  --mico-anim-default-timing: ease-out;                         /* Default timing function */
  --mico-anim-default-fill-mode: both;                          /* Default fill mode */

  /**
   * Interactive Animation Variables
   *
   * These variables control interactive elements like ripples and typewriter effects.
   */
  --mico-ripple-bg-default: rgba(255, 255, 255, 0.3);          /* Default ripple background */
  --mico-ripple-bg-dark: rgba(0, 0, 0, 0.2);                   /* Dark ripple background */
  --mico-typewriter-cursor-color: currentColor;                 /* Typewriter cursor color */

  /**
   * Animation Properties
   *
   * These variables define common animation presets for consistent usage.
   * Animations create movement and visual interest in the interface.
   */
  --mico-animation-none: none;        /* No animation */
  --mico-animation-spin: spin 1s linear infinite; /* Spinning animation */
  --mico-animation-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite; /* Ping/pulse effect */
  --mico-animation-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; /* Subtle pulse */
  --mico-animation-bounce: bounce 1s infinite; /* Bouncing effect */

  /* ====================================================================== */
  /* EASING SYSTEM                                                          */
  /* ====================================================================== */

  /**
   * Easing Functions
   *
   * These variables define timing functions that control animation pacing.
   * Different easing functions create different feelings of motion.
   */

  /* Standard Easing Functions */
  --mico-ease: cubic-bezier(0.25, 0.1, 0.25, 1.0); /* Standard ease */
  --mico-ease-in: cubic-bezier(0.42, 0, 1.0, 1.0); /* Slow start, fast end */
  --mico-ease-out: cubic-bezier(0, 0, 0.58, 1.0); /* Fast start, slow end */
  --mico-ease-in-out: cubic-bezier(0.42, 0, 0.58, 1.0); /* Slow start and end */

  /* Expressive Easing Functions */
  --mico-ease-elastic: cubic-bezier(0.68, -0.55, 0.265, 1.55); /* Elastic/bouncy */
  --mico-ease-bounce: cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Bouncy end */
  --mico-ease-back: cubic-bezier(0.68, -0.55, 0.265, 1.55); /* Slight overshoot */

  /* Physics-inspired Easing */
  --mico-ease-spring: cubic-bezier(0.5, 0.1, 0.1, 1); /* Spring-like motion */
  --mico-ease-gravity: cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Gravity effect */
  --mico-ease-snappy: cubic-bezier(0.1, 0.9, 0.2, 1); /* Quick, snappy motion */

  /* ====================================================================== */
  /* TRANSITION SYSTEM                                                      */
  /* ====================================================================== */

  /**
   * Transition Properties
   *
   * These variables define common transition presets for consistent usage.
   * Transitions create smooth animations between property changes.
   */

  /* Transition Durations */
  --mico-transition-duration-fast: 150ms;  /* Fast transitions */
  --mico-transition-duration-normal: 300ms; /* Standard transitions */
  --mico-transition-duration-slow: 500ms;  /* Slow transitions */

  /* Common Transitions */
  --mico-transition-all: all .4s var(--mico-ease); /* All properties */
  --mico-transition-color: color .4s var(--mico-ease); /* Color only */
  --mico-transition-background: background .4s var(--mico-ease); /* Background only */
  --mico-transition-border: border .4s var(--mico-ease); /* Border only */
  --mico-transition-opacity: opacity .4s var(--mico-ease); /* Opacity only */
  --mico-transition-transform: transform .4s var(--mico-ease); /* Transform only */
  --mico-transition-box-shadow: box-shadow .4s var(--mico-ease); /* Shadow only */

  /* ====================================================================== */
  /* COLOR SYSTEM                                                           */
  /* ====================================================================== */

  /**
   * Base Brand Colors
   *
   * These are the foundational brand colors that should be defined in the
   * styleguide.css file. They are referenced here to ensure they exist
   * for the color utility system to work properly.
   *
   * Note: These default values should be overridden in styleguide.css
   */
  --mico-color-primary: #3b82f6;     /* Primary brand color */
  --mico-color-secondary: #6b7280;   /* Secondary brand color */
  --mico-color-accent: #f59e0b;      /* Accent brand color */

  /**
   * Brand Color Variations
   *
   * Shades (darker variations) and tones (lighter variations) of brand colors.
   * These are dynamically generated from the base brand colors using HSL calculations.
   * This ensures they automatically adapt when users customize their brand colors.
   *
   * Browser Support: Modern browsers support hsl(from var()) syntax.
   * Fallbacks are provided for older browsers in the utility classes.
   */

     /* Primary Color Tones (lighter) - Dynamically generated */
  --mico-color-primary-light: hsl(from var(--mico-color-primary) h s calc(l + (100% - l) * 0.2));    /* Soft variation */
  --mico-color-primary-2xlight: hsl(from var(--mico-color-primary) h s calc(l + (100% - l) * 0.4));   /* Muted variation */
  --mico-color-primary-3xlight: hsl(from var(--mico-color-primary) h s calc(l + (100% - l) * 0.6));  /* Pastel variation */
  --mico-color-primary-4xlight: hsl(from var(--mico-color-primary) h s calc(l + (100% - l) * 0.8));    /* Pale variation */
  --mico-color-primary-5xlight: hsl(from var(--mico-color-primary) h s calc(l + (100% - l) * 0.9));   /* Faint variation */

  /* Primary Color Shades (darker) - Dynamically generated */
  --mico-color-primary-dark: hsl(from var(--mico-color-primary) h s calc(l * 0.8));    /* 80% lightness */
  --mico-color-primary-2xdark: hsl(from var(--mico-color-primary) h s calc(l * 0.6));   /* 60% lightness */
  --mico-color-primary-3xdark: hsl(from var(--mico-color-primary) h s calc(l * 0.4));     /* 40% lightness */
  --mico-color-primary-4xdark: hsl(from var(--mico-color-primary) h s calc(l * 0.2));   /* 20% lightness */
  --mico-color-primary-5xdark: hsl(from var(--mico-color-primary) h s calc(l * 0.1));  /* 10% lightness */

  /* Secondary Color Tones (lighter) - Dynamically generated */
  --mico-color-secondary-light: hsl(from var(--mico-color-secondary) h s calc(l + (100% - l) * 0.2));    /* Soft variation */
  --mico-color-secondary-2xlight: hsl(from var(--mico-color-secondary) h s calc(l + (100% - l) * 0.4));   /* Muted variation */
  --mico-color-secondary-3xlight: hsl(from var(--mico-color-secondary) h s calc(l + (100% - l) * 0.6));  /* Pastel variation */
  --mico-color-secondary-4xlight: hsl(from var(--mico-color-secondary) h s calc(l + (100% - l) * 0.8));    /* Pale variation */
  --mico-color-secondary-5xlight: hsl(from var(--mico-color-secondary) h s calc(l + (100% - l) * 0.9));   /* Faint variation */

  /* Secondary Color Shades (darker) - Dynamically generated */
  --mico-color-secondary-dark: hsl(from var(--mico-color-secondary) h s calc(l * 0.8));    /* 80% lightness */
  --mico-color-secondary-2xdark: hsl(from var(--mico-color-secondary) h s calc(l * 0.6));   /* 60% lightness */
  --mico-color-secondary-3xdark: hsl(from var(--mico-color-secondary) h s calc(l * 0.4));     /* 40% lightness */
  --mico-color-secondary-4xdark: hsl(from var(--mico-color-secondary) h s calc(l * 0.2));   /* 20% lightness */
  --mico-color-secondary-5xdark: hsl(from var(--mico-color-secondary) h s calc(l * 0.1));  /* 10% lightness */

  /* Accent Color Tones (lighter) - Dynamically generated */
  --mico-color-accent-light: hsl(from var(--mico-color-accent) h s calc(l + (100% - l) * 0.2));    /* Soft variation */
  --mico-color-accent-2xlight: hsl(from var(--mico-color-accent) h s calc(l + (100% - l) * 0.4));   /* Muted variation */
  --mico-color-accent-3xlight: hsl(from var(--mico-color-accent) h s calc(l + (100% - l) * 0.6));  /* Pastel variation */
  --mico-color-accent-4xlight: hsl(from var(--mico-color-accent) h s calc(l + (100% - l) * 0.8));    /* Pale variation */
  --mico-color-accent-5xlight: hsl(from var(--mico-color-accent) h s calc(l + (100% - l) * 0.9));   /* Faint variation */

  /* Accent Color Shades (darker) - Dynamically generated */
  --mico-color-accent-dark: hsl(from var(--mico-color-accent) h s calc(l * 0.8));    /* 80% lightness */
  --mico-color-accent-2xdark: hsl(from var(--mico-color-accent) h s calc(l * 0.6));   /* 60% lightness */
  --mico-color-accent-3xdark: hsl(from var(--mico-color-accent) h s calc(l * 0.4));     /* 40% lightness */
  --mico-color-accent-4xdark: hsl(from var(--mico-color-accent) h s calc(l * 0.2));   /* 20% lightness */
  --mico-color-accent-5xdark: hsl(from var(--mico-color-accent) h s calc(l * 0.1));  /* 10% lightness */

  /**
   * Semantic Colors
   *
   * Colors that convey meaning and state information.
   * These maintain WCAG AA contrast ratios for accessibility.
   */
  --mico-color-success: #10b981;     /* Success state - WCAG AA compliant */
  --mico-color-warning: #f59e0b;     /* Warning state - WCAG AA compliant */
  --mico-color-error: #ef4444;       /* Error state - WCAG AA compliant */
  --mico-color-info: #3b82f6;        /* Information state - WCAG AA compliant */
  --mico-color-visited: #8b5cf6;     /* Visited links - distinct from primary */

  /**
   * Neutral Colors - Black Scale
   *
   * Black color variations from pure black to lighter shades.
   * These provide depth and contrast in dark themes.
   */
  --mico-color-black-100: #000000;       /* Pure black */
  --mico-color-black-200: #0d0d0d;       /* 5% lightness */
  --mico-color-black-300: #1a1a1a;       /* 10% lightness */
  --mico-color-black-400: #262626;       /* 15% lightness */
  --mico-color-black-500: #333333;       /* 20% lightness */
  --mico-color-black-600: #404040;       /* 25% lightness */
  --mico-color-black-700: #4d4d4d;       /* 30% lightness */
  --mico-color-black-800: #595959;       /* 35% lightness */
  --mico-color-black-900: #666666;       /* 40% lightness */

  /**
   * Neutral Colors - Gray Scale
   *
   * Comprehensive gray palette for UI elements, text, and backgrounds.
   * These colors are carefully chosen for optimal contrast and accessibility.
   */
  --mico-color-gray-100: #f3f4f6;        /* Lightest gray - backgrounds */
  --mico-color-gray-200: #e5e7eb;        /* Light gray - borders, dividers */
  --mico-color-gray-300: #d1d5db;        /* Medium-light gray - disabled states */
  --mico-color-gray-400: #9ca3af;        /* Medium gray - placeholder text */
  --mico-color-gray-500: #6b7280;        /* True gray - secondary text */
  --mico-color-gray-600: #4b5563;        /* Medium-dark gray - body text */
  --mico-color-gray-700: #374151;        /* Dark gray - headings */
  --mico-color-gray-800: #1f2937;        /* Darker gray - primary text */
  --mico-color-gray-900: #111827;        /* Darkest gray - high contrast text */

  /**
   * Neutral Colors - White Scale
   *
   * White color variations for subtle backgrounds and overlays.
   * These provide clean, minimal aesthetics in light themes.
   */
  --mico-color-white-100: #ffffff;       /* Pure white */
  --mico-color-white-200: #fafafa;       /* 98% lightness */
  --mico-color-white-300: #f5f5f5;       /* 96% lightness */
  --mico-color-white-400: #f0f0f0;       /* 94% lightness */
  --mico-color-white-500: #ebebeb;       /* 92% lightness */
  --mico-color-white-600: #e6e6e6;       /* 90% lightness */
  --mico-color-white-700: #e0e0e0;       /* 88% lightness */
  --mico-color-white-800: #dbdbdb;       /* 86% lightness */
  --mico-color-white-900: #d6d6d6;       /* 84% lightness */

  /**
   * Transparency Colors
   *
   * Semi-transparent overlays and backgrounds for modals, dropdowns, and effects.
   * These maintain consistent opacity levels across the framework.
   */

  /* Black Transparencies */
  --mico-color-black-trans-100: rgba(0, 0, 0, 0.1);   /* 10% opacity */
  --mico-color-black-trans-200: rgba(0, 0, 0, 0.2);   /* 20% opacity */
  --mico-color-black-trans-300: rgba(0, 0, 0, 0.3);   /* 30% opacity */
  --mico-color-black-trans-400: rgba(0, 0, 0, 0.4);   /* 40% opacity */
  --mico-color-black-trans-500: rgba(0, 0, 0, 0.5);   /* 50% opacity */
  --mico-color-black-trans-600: rgba(0, 0, 0, 0.6);   /* 60% opacity */
  --mico-color-black-trans-700: rgba(0, 0, 0, 0.7);   /* 70% opacity */
  --mico-color-black-trans-800: rgba(0, 0, 0, 0.8);   /* 80% opacity */
  --mico-color-black-trans-900: rgba(0, 0, 0, 0.9);   /* 90% opacity */

  /* Gray Transparencies */
  --mico-color-gray-trans-100: rgba(107, 114, 128, 0.1);  /* 10% opacity */
  --mico-color-gray-trans-200: rgba(107, 114, 128, 0.2);  /* 20% opacity */
  --mico-color-gray-trans-300: rgba(107, 114, 128, 0.3);  /* 30% opacity */
  --mico-color-gray-trans-400: rgba(107, 114, 128, 0.4);  /* 40% opacity */
  --mico-color-gray-trans-500: rgba(107, 114, 128, 0.5);  /* 50% opacity */
  --mico-color-gray-trans-600: rgba(107, 114, 128, 0.6);  /* 60% opacity */
  --mico-color-gray-trans-700: rgba(107, 114, 128, 0.7);  /* 70% opacity */
  --mico-color-gray-trans-800: rgba(107, 114, 128, 0.8);  /* 80% opacity */
  --mico-color-gray-trans-900: rgba(107, 114, 128, 0.9);  /* 90% opacity */

  /* White Transparencies */
  --mico-color-white-trans-100: rgba(255, 255, 255, 0.1); /* 10% opacity */
  --mico-color-white-trans-200: rgba(255, 255, 255, 0.2); /* 20% opacity */
  --mico-color-white-trans-300: rgba(255, 255, 255, 0.3); /* 30% opacity */
  --mico-color-white-trans-400: rgba(255, 255, 255, 0.4); /* 40% opacity */
  --mico-color-white-trans-500: rgba(255, 255, 255, 0.5); /* 50% opacity */
  --mico-color-white-trans-600: rgba(255, 255, 255, 0.6); /* 60% opacity */
  --mico-color-white-trans-700: rgba(255, 255, 255, 0.7); /* 70% opacity */
  --mico-color-white-trans-800: rgba(255, 255, 255, 0.8); /* 80% opacity */
  --mico-color-white-trans-900: rgba(255, 255, 255, 0.9); /* 90% opacity */

  /**
   * Text Colors
   *
   * Standard text colors for consistent typography.
   * These adapt to light/dark mode automatically.
   */
  --mico-color-text-primary: #111827;    /* Primary text */
  --mico-color-text-secondary: #6b7280;  /* Secondary text */
  --mico-color-text-muted: #9ca3af;      /* Muted text */
  --mico-color-text-inverse: #ffffff;    /* Inverse text (for dark backgrounds) */

  /**
   * Background Colors
   *
   * Standard background colors for consistent layouts.
   */
  --mico-color-bg-primary: #ffffff;      /* Primary background */
  --mico-color-bg-secondary: #f9fafb;    /* Secondary background */
  --mico-color-bg-muted: #f3f4f6;        /* Muted background */
  --mico-color-bg-inverse: #111827;      /* Inverse background */

  /**
   * Border Colors
   *
   * Standard border colors for consistent UI elements.
   */
  --mico-color-border-primary: #e5e7eb;  /* Primary border */
  --mico-color-border-secondary: #d1d5db; /* Secondary border */
  --mico-color-border-muted: #f3f4f6;    /* Muted border */
  --mico-color-border-focus: #3b82f6;    /* Focus border */

  /**
   * Extended Color Palette
   *
   * Comprehensive color palette for diverse design needs.
   * Each color includes 9 variations from light (100) to dark (900).
   * All colors are tested for accessibility and contrast compliance.
   */

  /* Red Color Scale - Error states, alerts, danger */
  --mico-color-red-100: #fee2e2;         /* Lightest red - error backgrounds */
  --mico-color-red-200: #fecaca;         /* Light red - error borders */
  --mico-color-red-300: #fca5a5;         /* Medium-light red */
  --mico-color-red-400: #f87171;         /* Medium red */
  --mico-color-red-500: #ef4444;         /* True red - primary error color */
  --mico-color-red-600: #dc2626;         /* Medium-dark red */
  --mico-color-red-700: #b91c1c;         /* Dark red */
  --mico-color-red-800: #991b1b;         /* Darker red */
  --mico-color-red-900: #7f1d1d;         /* Darkest red */

  /* Yellow Color Scale - Warning states, highlights */
  --mico-color-yellow-100: #fef3c7;      /* Lightest yellow - warning backgrounds */
  --mico-color-yellow-200: #fde68a;      /* Light yellow */
  --mico-color-yellow-300: #fcd34d;      /* Medium-light yellow */
  --mico-color-yellow-400: #fbbf24;      /* Medium yellow */
  --mico-color-yellow-500: #f59e0b;      /* True yellow - primary warning color */
  --mico-color-yellow-600: #d97706;      /* Medium-dark yellow */
  --mico-color-yellow-700: #b45309;      /* Dark yellow */
  --mico-color-yellow-800: #92400e;      /* Darker yellow */
  --mico-color-yellow-900: #78350f;      /* Darkest yellow */

  /* Green Color Scale - Success states, positive actions */
  --mico-color-green-100: #d1fae5;       /* Lightest green - success backgrounds */
  --mico-color-green-200: #a7f3d0;       /* Light green */
  --mico-color-green-300: #6ee7b7;       /* Medium-light green */
  --mico-color-green-400: #34d399;       /* Medium green */
  --mico-color-green-500: #10b981;       /* True green - primary success color */
  --mico-color-green-600: #059669;       /* Medium-dark green */
  --mico-color-green-700: #047857;       /* Dark green */
  --mico-color-green-800: #065f46;       /* Darker green */
  --mico-color-green-900: #064e3b;       /* Darkest green */

  /* Blue Color Scale - Information, links, primary actions */
  --mico-color-blue-100: #dbeafe;        /* Lightest blue - info backgrounds */
  --mico-color-blue-200: #bfdbfe;        /* Light blue */
  --mico-color-blue-300: #93c5fd;        /* Medium-light blue */
  --mico-color-blue-400: #60a5fa;        /* Medium blue */
  --mico-color-blue-500: #3b82f6;        /* True blue - primary info color */
  --mico-color-blue-600: #2563eb;        /* Medium-dark blue */
  --mico-color-blue-700: #1d4ed8;        /* Dark blue */
  --mico-color-blue-800: #1e40af;        /* Darker blue */
  --mico-color-blue-900: #1e3a8a;        /* Darkest blue */

  /* Indigo Color Scale - Deep blues, professional themes */
  --mico-color-indigo-100: #e0e7ff;      /* Lightest indigo */
  --mico-color-indigo-200: #c7d2fe;      /* Light indigo */
  --mico-color-indigo-300: #a5b4fc;      /* Medium-light indigo */
  --mico-color-indigo-400: #818cf8;      /* Medium indigo */
  --mico-color-indigo-500: #6366f1;      /* True indigo */
  --mico-color-indigo-600: #4f46e5;      /* Medium-dark indigo */
  --mico-color-indigo-700: #4338ca;      /* Dark indigo */
  --mico-color-indigo-800: #3730a3;      /* Darker indigo */
  --mico-color-indigo-900: #312e81;      /* Darkest indigo */

  /* Purple Color Scale - Creative themes, luxury */
  --mico-color-purple-100: #ede9fe;      /* Lightest purple */
  --mico-color-purple-200: #ddd6fe;      /* Light purple */
  --mico-color-purple-300: #c4b5fd;      /* Medium-light purple */
  --mico-color-purple-400: #a78bfa;      /* Medium purple */
  --mico-color-purple-500: #8b5cf6;      /* True purple */
  --mico-color-purple-600: #7c3aed;      /* Medium-dark purple */
  --mico-color-purple-700: #6d28d9;      /* Dark purple */
  --mico-color-purple-800: #5b21b6;      /* Darker purple */
  --mico-color-purple-900: #4c1d95;      /* Darkest purple */

  /* Pink Color Scale - Feminine themes, highlights */
  --mico-color-pink-100: #fce7f3;        /* Lightest pink */
  --mico-color-pink-200: #fbcfe8;        /* Light pink */
  --mico-color-pink-300: #f9a8d4;        /* Medium-light pink */
  --mico-color-pink-400: #f472b6;        /* Medium pink */
  --mico-color-pink-500: #ec4899;        /* True pink */
  --mico-color-pink-600: #db2777;        /* Medium-dark pink */
  --mico-color-pink-700: #be185d;        /* Dark pink */
  --mico-color-pink-800: #9d174d;        /* Darker pink */
  --mico-color-pink-900: #831843;        /* Darkest pink */
}