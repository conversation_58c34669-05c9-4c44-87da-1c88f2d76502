/* ========================================================================== */
/* MICO CSS FRAMEWORK - TYPOGRAPHY UTILITIES                                 */
/* ========================================================================== */

/* ========================================================================== */
/* FONT FAMILY UTILITIES                                                     */
/* ========================================================================== */

.f-sans { font-family: var(--mico-font-sans) !important; }
.f-serif { font-family: var(--mico-font-serif) !important; }
.f-mono { font-family: var(--mico-font-mono) !important; }
.f-display { font-family: var(--mico-font-display) !important; }
.f-body { font-family: var(--mico-font-body) !important; }

/* ========================================================================== */
/* FONT SIZE UTILITIES                                                       */
/* ========================================================================== */

.fs-xs { font-size: var(--mico-fs-xs) !important; }
.fs-sm { font-size: var(--mico-fs-sm) !important; }
.fs-md { font-size: var(--mico-fs-md) !important; }
.fs-lg { font-size: var(--mico-fs-lg) !important; }
.fs-xl { font-size: var(--mico-fs-xl) !important; }
.fs-2xl { font-size: var(--mico-fs-2xl) !important; }
.fs-3xl { font-size: var(--mico-fs-3xl) !important; }
.fs-4xl { font-size: var(--mico-fs-4xl) !important; }
.fs-5xl { font-size: var(--mico-fs-5xl) !important; }
.fs-6xl { font-size: var(--mico-fs-6xl) !important; }
.fs-7xl { font-size: var(--mico-fs-7xl) !important; }
.fs-8xl { font-size: var(--mico-fs-8xl) !important; }
.fs-9xl { font-size: var(--mico-fs-9xl) !important; }

/* ========================================================================== */
/* FONT WEIGHT UTILITIES                                                     */
/* ========================================================================== */

.fw-100 { font-weight: var(--mico-fw-100) !important; }
.fw-200 { font-weight: var(--mico-fw-200) !important; }
.fw-300 { font-weight: var(--mico-fw-300) !important; }
.fw-400 { font-weight: var(--mico-fw-400) !important; }
.fw-500 { font-weight: var(--mico-fw-500) !important; }
.fw-600 { font-weight: var(--mico-fw-600) !important; }
.fw-700 { font-weight: var(--mico-fw-700) !important; }
.fw-800 { font-weight: var(--mico-fw-800) !important; }
.fw-900 { font-weight: var(--mico-fw-900) !important; }

/* ========================================================================== */
/* LINE HEIGHT UTILITIES                                                     */
/* ========================================================================== */

.lh-xs { line-height: var(--mico-lh-xs) !important; }
.lh-sm { line-height: var(--mico-lh-sm) !important; }
.lh-md { line-height: var(--mico-lh-md) !important; }
.lh-lg { line-height: var(--mico-lh-lg) !important; }
.lh-xl { line-height: var(--mico-lh-xl) !important; }
.lh-2xl { line-height: var(--mico-lh-2xl) !important; }
.lh-3xl { line-height: var(--mico-lh-3xl) !important; }
.lh-4xl { line-height: var(--mico-lh-4xl) !important; }
.lh-5xl { line-height: var(--mico-lh-5xl) !important; }
.lh-6xl { line-height: var(--mico-lh-6xl) !important; }
.lh-7xl { line-height: var(--mico-lh-7xl) !important; }
.lh-8xl { line-height: var(--mico-lh-8xl) !important; }
.lh-9xl { line-height: var(--mico-lh-9xl) !important; }

/* ========================================================================== */
/* LETTER SPACING UTILITIES                                                  */
/* ========================================================================== */

.ls-xs { letter-spacing: var(--mico-ls-xs) !important; }
.ls-sm { letter-spacing: var(--mico-ls-sm) !important; }
.ls-md { letter-spacing: var(--mico-ls-md) !important; }
.ls-lg { letter-spacing: var(--mico-ls-lg) !important; }
.ls-xl { letter-spacing: var(--mico-ls-xl) !important; }
.ls-2xl { letter-spacing: var(--mico-ls-2xl) !important; }

/* ========================================================================== */
/* FONT STYLE UTILITIES                                                      */
/* ========================================================================== */

.f-normal { font-style: var(--mico-font-style-normal) !important; }
.f-italic { font-style: var(--mico-font-style-italic) !important; }

/* ========================================================================== */
/* FONT STRETCH UTILITIES                                                    */
/* ========================================================================== */

.f-stretch-ultra-condensed { font-stretch: var(--mico-font-stretch-ultra-condensed) !important; }
.f-stretch-extra-condensed { font-stretch: var(--mico-font-stretch-extra-condensed) !important; }
.f-stretch-condensed { font-stretch: var(--mico-font-stretch-condensed) !important; }
.f-stretch-semi-condensed { font-stretch: var(--mico-font-stretch-semi-condensed) !important; }
.f-stretch-normal { font-stretch: var(--mico-font-stretch-normal) !important; }
.f-stretch-semi-expanded { font-stretch: var(--mico-font-stretch-semi-expanded) !important; }
.f-stretch-expanded { font-stretch: var(--mico-font-stretch-expanded) !important; }
.f-stretch-extra-expanded { font-stretch: var(--mico-font-stretch-extra-expanded) !important; }
.f-stretch-ultra-expanded { font-stretch: var(--mico-font-stretch-ultra-expanded) !important; }

/* ========================================================================== */
/* TEXT ALIGNMENT UTILITIES                                                  */
/* ========================================================================== */

.text-left { text-align: var(--mico-text-align-left) !important; }
.text-center { text-align: var(--mico-text-align-center) !important; }
.text-right { text-align: var(--mico-text-align-right) !important; }
.text-justify { text-align: var(--mico-text-align-justify) !important; }
.text-start { text-align: var(--mico-text-align-start) !important; }
.text-end { text-align: var(--mico-text-align-end) !important; }

/* ========================================================================== */
/* TEXT TRANSFORM UTILITIES                                                  */
/* ========================================================================== */

.text-uppercase { text-transform: var(--mico-text-transform-uppercase) !important; }
.text-lowercase { text-transform: var(--mico-text-transform-lowercase) !important; }
.text-capitalize { text-transform: var(--mico-text-transform-capitalize) !important; }
.text-normal-case { text-transform: var(--mico-text-transform-none) !important; }

/* ========================================================================== */
/* TEXT DECORATION UTILITIES                                                 */
/* ========================================================================== */

.underline { text-decoration-line: underline !important; }
.overline { text-decoration-line: overline !important; }
.line-through { text-decoration-line: line-through !important; }
.no-underline { text-decoration-line: var(--mico-value-none) !important; }

/* Text Decoration Style */
.decoration-solid { text-decoration-style: var(--mico-decoration-style-solid) !important; }
.decoration-double { text-decoration-style: var(--mico-decoration-style-double) !important; }
.decoration-dotted { text-decoration-style: var(--mico-decoration-style-dotted) !important; }
.decoration-dashed { text-decoration-style: var(--mico-decoration-style-dashed) !important; }
.decoration-wavy { text-decoration-style: var(--mico-decoration-style-wavy) !important; }

/* Text Decoration Thickness */
.decoration-auto { text-decoration-thickness: var(--mico-decoration-thickness-auto) !important; }
.decoration-from-font { text-decoration-thickness: var(--mico-decoration-thickness-from-font) !important; }
.decoration-0 { text-decoration-thickness: var(--mico-decoration-thickness-0) !important; }
.decoration-1 { text-decoration-thickness: var(--mico-decoration-thickness-1) !important; }
.decoration-2 { text-decoration-thickness: var(--mico-decoration-thickness-2) !important; }
.decoration-4 { text-decoration-thickness: var(--mico-decoration-thickness-4) !important; }
.decoration-8 { text-decoration-thickness: var(--mico-decoration-thickness-8) !important; }

/* Text Underline Offset */
.underline-offset-auto { text-underline-offset: var(--mico-underline-offset-auto) !important; }
.underline-offset-0 { text-underline-offset: var(--mico-underline-offset-0) !important; }
.underline-offset-1 { text-underline-offset: var(--mico-underline-offset-1) !important; }
.underline-offset-2 { text-underline-offset: var(--mico-underline-offset-2) !important; }
.underline-offset-4 { text-underline-offset: var(--mico-underline-offset-4) !important; }
.underline-offset-8 { text-underline-offset: var(--mico-underline-offset-8) !important; }

/* ========================================================================== */
/* TEXT OVERFLOW UTILITIES                                                   */
/* ========================================================================== */

.text-ellipsis { text-overflow: var(--mico-text-overflow-ellipsis) !important; }
.text-clip { text-overflow: var(--mico-text-overflow-clip) !important; }

.truncate {
  overflow: hidden !important;
  text-overflow: var(--mico-text-overflow-ellipsis) !important;
  white-space: var(--mico-whitespace-nowrap) !important;
}

/* ========================================================================== */
/* WHITESPACE UTILITIES                                                      */
/* ========================================================================== */

.whitespace-normal { white-space: var(--mico-whitespace-normal) !important; }
.whitespace-nowrap { white-space: var(--mico-whitespace-nowrap) !important; }
.whitespace-pre { white-space: var(--mico-whitespace-pre) !important; }
.whitespace-pre-line { white-space: var(--mico-whitespace-pre-line) !important; }
.whitespace-pre-wrap { white-space: var(--mico-whitespace-pre-wrap) !important; }
.whitespace-break-spaces { white-space: var(--mico-whitespace-break-spaces) !important; }

/* ========================================================================== */
/* WORD BREAK UTILITIES                                                      */
/* ========================================================================== */

.break-normal {
  overflow-wrap: var(--mico-overflow-wrap-normal) !important;
  word-break: var(--mico-word-break-normal) !important;
}
.break-words { overflow-wrap: var(--mico-overflow-wrap-break-word) !important; }
.break-all { word-break: var(--mico-word-break-break-all) !important; }
.break-keep { word-break: var(--mico-word-break-keep-all) !important; }

/* ========================================================================== */
/* TEXT DIRECTION UTILITIES                                                  */
/* ========================================================================== */

.text-ltr { direction: var(--mico-text-direction-ltr) !important; }
.text-rtl { direction: var(--mico-text-direction-rtl) !important; }

/* ========================================================================== */
/* VERTICAL ALIGNMENT UTILITIES                                              */
/* ========================================================================== */

.align-baseline { vertical-align: baseline !important; }
.align-top { vertical-align: top !important; }
.align-middle { vertical-align: middle !important; }
.align-bottom { vertical-align: bottom !important; }
.align-text-top { vertical-align: text-top !important; }
.align-text-bottom { vertical-align: text-bottom !important; }
.align-sub { vertical-align: sub !important; }
.align-super { vertical-align: super !important; }

/* ========================================================================== */
/* TEXT INDENT UTILITIES                                                     */
/* ========================================================================== */

.indent-0 { text-indent: var(--mico-indent-0) !important; }
.indent-xs { text-indent: var(--mico-indent-xs) !important; }
.indent-sm { text-indent: var(--mico-indent-sm) !important; }
.indent-md { text-indent: var(--mico-indent-md) !important; }
.indent-lg { text-indent: var(--mico-indent-lg) !important; }
.indent-xl { text-indent: var(--mico-indent-xl) !important; }

/* ========================================================================== */
/* TEXT SHADOW UTILITIES                                                     */
/* ========================================================================== */

.text-shadow-none { text-shadow: var(--mico-text-shadow-none) !important; }
.text-shadow-xs { text-shadow: var(--mico-text-shadow-xs) !important; }
.text-shadow-sm { text-shadow: var(--mico-text-shadow-sm) !important; }
.text-shadow-md { text-shadow: var(--mico-text-shadow-md) !important; }
.text-shadow-lg { text-shadow: var(--mico-text-shadow-lg) !important; }

/* ========================================================================== */
/* TEXT STROKE UTILITIES                                                     */
/* ========================================================================== */

.text-stroke-xs { -webkit-text-stroke-width: var(--mico-text-stroke-xs) !important; }
.text-stroke-sm { -webkit-text-stroke-width: var(--mico-text-stroke-sm) !important; }
.text-stroke-md { -webkit-text-stroke-width: var(--mico-text-stroke-md) !important; }

/* ========================================================================== */
/* FONT VARIANT UTILITIES                                                    */
/* ========================================================================== */

/* Font Variant Numeric */
.font-variant-normal { font-variant-numeric: var(--mico-font-variant-numeric-normal) !important; }
.font-variant-ordinal { font-variant-numeric: var(--mico-font-variant-numeric-ordinal) !important; }
.font-variant-slashed-zero { font-variant-numeric: var(--mico-font-variant-numeric-slashed-zero) !important; }
.font-variant-lining-nums { font-variant-numeric: var(--mico-font-variant-numeric-lining-nums) !important; }
.font-variant-oldstyle-nums { font-variant-numeric: var(--mico-font-variant-numeric-oldstyle-nums) !important; }
.font-variant-proportional-nums { font-variant-numeric: var(--mico-font-variant-numeric-proportional-nums) !important; }
.font-variant-tabular-nums { font-variant-numeric: var(--mico-font-variant-numeric-tabular-nums) !important; }
.font-variant-diagonal-fractions { font-variant-numeric: var(--mico-font-variant-numeric-diagonal-fractions) !important; }
.font-variant-stacked-fractions { font-variant-numeric: var(--mico-font-variant-numeric-stacked-fractions) !important; }

/* Font Variant Ligatures */
.ligatures-common { font-variant-ligatures: var(--mico-font-variant-ligatures-common) !important; }
.ligatures-no-common { font-variant-ligatures: var(--mico-font-variant-ligatures-no-common) !important; }
.ligatures-discretionary { font-variant-ligatures: var(--mico-font-variant-ligatures-discretionary) !important; }
.ligatures-no-discretionary { font-variant-ligatures: var(--mico-font-variant-ligatures-no-discretionary) !important; }
.ligatures-historical { font-variant-ligatures: var(--mico-font-variant-ligatures-historical) !important; }
.ligatures-no-historical { font-variant-ligatures: var(--mico-font-variant-ligatures-no-historical) !important; }
.ligatures-contextual { font-variant-ligatures: var(--mico-font-variant-ligatures-contextual) !important; }
.ligatures-no-contextual { font-variant-ligatures: var(--mico-font-variant-ligatures-no-contextual) !important; }

/* Font Variant Caps */
.caps-normal { font-variant-caps: var(--mico-font-variant-caps-normal) !important; }
.caps-small { font-variant-caps: var(--mico-font-variant-caps-small-caps) !important; }
.caps-all-small { font-variant-caps: var(--mico-font-variant-caps-all-small-caps) !important; }
.caps-petite { font-variant-caps: var(--mico-font-variant-caps-petite-caps) !important; }
.caps-all-petite { font-variant-caps: var(--mico-font-variant-caps-all-petite-caps) !important; }
.caps-unicase { font-variant-caps: var(--mico-font-variant-caps-unicase) !important; }
.caps-titling { font-variant-caps: var(--mico-font-variant-caps-titling-caps) !important; }

/* ========================================================================== */
/* LIST STYLE UTILITIES                                                      */
/* ========================================================================== */

.list-none { list-style-type: var(--mico-list-style-type-none) !important; }
.list-disc { list-style-type: var(--mico-list-style-type-disc) !important; }
.list-decimal { list-style-type: var(--mico-list-style-type-decimal) !important; }
.list-square { list-style-type: var(--mico-list-style-type-square) !important; }
.list-upper-roman { list-style-type: var(--mico-list-style-type-upper-roman) !important; }
.list-lower-roman { list-style-type: var(--mico-list-style-type-lower-roman) !important; }
.list-upper-alpha { list-style-type: var(--mico-list-style-type-upper-alpha) !important; }
.list-lower-alpha { list-style-type: var(--mico-list-style-type-lower-alpha) !important; }

.list-inside { list-style-position: var(--mico-list-style-position-inside) !important; }
.list-outside { list-style-position: var(--mico-list-style-position-outside) !important; }

/* ========================================================================== */
/* USER SELECT UTILITIES                                                     */
/* ========================================================================== */

.select-none { user-select: var(--mico-user-select-none) !important; }
.select-text { user-select: var(--mico-user-select-text) !important; }
.select-all { user-select: var(--mico-user-select-all) !important; }
.select-auto { user-select: var(--mico-user-select-auto) !important; }

/* ========================================================================== */
/* WRITING MODE UTILITIES                                                    */
/* ========================================================================== */

.writing-horizontal { writing-mode: var(--mico-writing-mode-horizontal-tb) !important; }
.writing-vertical-rl { writing-mode: var(--mico-writing-mode-vertical-rl) !important; }
.writing-vertical-lr { writing-mode: var(--mico-writing-mode-vertical-lr) !important; }

/* ========================================================================== */
/* TEXT ORIENTATION UTILITIES                                                */
/* ========================================================================== */

.orientation-mixed { text-orientation: var(--mico-text-orientation-mixed) !important; }
.orientation-upright { text-orientation: var(--mico-text-orientation-upright) !important; }
.orientation-sideways { text-orientation: var(--mico-text-orientation-sideways) !important; }

/* ========================================================================== */
/* HYPHENS UTILITIES                                                         */
/* ========================================================================== */

.hyphens-none { hyphens: var(--mico-hyphens-none) !important; }
.hyphens-manual { hyphens: var(--mico-hyphens-manual) !important; }
.hyphens-auto { hyphens: var(--mico-hyphens-auto) !important; }

/* ========================================================================== */
/* ADVANCED TYPOGRAPHY FEATURES                                              */
/* ========================================================================== */

.drop-cap::first-letter {
  float: left;
  font-size: 3em;
  line-height: 0.8;
  padding-right: 0.1em;
  padding-top: 0.1em;
}

.text-wrap-balance { text-wrap: balance; }
.text-wrap-pretty { text-wrap: pretty; }
.text-wrap-stable { text-wrap: stable; }

.gradient-text {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

/* ========================================================================== */
/* FONT SMOOTHING UTILITIES                                                  */
/* ========================================================================== */

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.subpixel-antialiased {
  -webkit-font-smoothing: auto;
  -moz-osx-font-smoothing: auto;
}











