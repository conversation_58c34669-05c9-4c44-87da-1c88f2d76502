/**
 * Color Utility Classes
 *
 * Comprehensive color utilities for the Mico CSS Framework.
 * All color variables are defined in css/base/variables.css
 *
 * This file contains only utility classes that reference those variables.
 * Classes are organized by: Brand Colors, Neutral Colors, Extended Palette,
 * Transparency Colors, and Semantic Colors.
 */


/* ====================================================================== */
/* BRAND COLOR UTILITIES                                                  */
/* ====================================================================== */

/**
 * Base Brand Colors
 * Primary, secondary, and accent colors for brand consistency
 */

/* Base Brand Background Colors */
.bg-primary { background-color: var(--mico-color-primary) !important; }
.bg-secondary { background-color: var(--mico-color-secondary) !important; }
.bg-accent { background-color: var(--mico-color-accent) !important; }

/* Base Brand Text Colors */
.text-primary { color: var(--mico-color-primary) !important; }
.text-secondary { color: var(--mico-color-secondary) !important; }
.text-accent { color: var(--mico-color-accent) !important; }

/**
 * Primary Color Variations
 * Shades (darker) and tones (lighter) of the primary brand color
 */

/* Primary Tint Text Colors (lighter)  */
.text-primary-ligth { color: var(--mico-color-primary-ligth) !important; }
.text-primary-2xlight { color: var(--mico-color-primary-2xlight) !important; }
.text-primary-3xlight {color: var(--mico-color-primary-3xlight) !important; }
.text-primary-4xlight { color: var(--mico-color-primary-4xlight) !important; }
.text-primary-5xlight { color: var(--mico-color-primary-5xlight) !important; }

/* Primary Shade Text Colors (darker)  */
.text-primary-dark { color: var(--mico-color-primary-dark) !important; }
.text-primary-2xdark { color: var(--mico-color-primary-2xdark) !important; }
.text-primary-3xdark { color: var(--mico-color-primary-3xdark) !important; }
.text-primary-4xdark { color: var(--mico-color-primary-4xdark) !important;}
.text-primary-5xdark { color: var(--mico-color-primary-5xdark) !important; }

/* Primary Tint Background Colors (lighter)  */
.bg-primary-ligth { background-color: var(--mico-color-primary-ligth) !important; }
.bg-primary-2xlight { background-color: var(--mico-color-primary-2xlight) !important; }
.bg-primary-3xlight { background-color: var(--mico-color-primary-3xlight) !important; }
.bg-primary-4xlight { background-color: var(--mico-color-primary-4xlight) !important; }
.bg-primary-5xlight { background-color: var(--mico-color-primary-5xlight) !important; }

/* Primary Shade Background Colors (darker)  */
.bg-primary-dark { background-color: var(--mico-color-primary-dark) !important }
.bg-primary-2xdark { background-color: var(--mico-color-primary-2xdark) !important; }
.bg-primary-3xdark { background-color: var(--mico-color-primary-3xdark) !important; }
.bg-primary-4xdark { background-color: var(--mico-color-primary-4xdark) !important; }
.bg-primary-5xdark { background-color: var(--mico-color-primary-5xdark) !important; }

/**
 * Secondary Color Variations
 * Shades (darker) and tones (lighter) of the secondary brand color
 */

/* Secondary Tint Text Colors (lighter) */
.text-secondary-ligth { color: var(--mico-color-secondary-ligth) !important; }
.text-secondary-2xlight { color: var(--mico-color-secondary-2xlight) !important; }
.text-secondary-3xlight { color: var(--mico-color-secondary-3xlight) !important; }
.text-secondary-4xlight { color: var(--mico-color-secondary-4xlight) !important; }
.text-secondary-5xlight { color: var(--mico-color-secondary-5xlight) !important; }

/* Secondary Shade Text Colors (darker) */
.text-secondary-dark { color: var(--mico-color-secondary-dark) !important; }
.text-secondary-2xdark { color: var(--mico-color-secondary-2xdark) !important; }
.text-secondary-3xdark { color: var(--mico-color-secondary-3xdark) !important; }
.text-secondary-4xdark { color: var(--mico-color-secondary-4xdark) !important; }
.text-secondary-5xdark { color: var(--mico-color-secondary-5xdark) !important; }

/* Secondary Tint Background Colors (lighter) */
.bg-secondary-ligth { background-color: var(--mico-color-secondary-ligth) !important; }
.bg-secondary-2xlight { background-color: var(--mico-color-secondary-2xlight) !important; }
.bg-secondary-3xlight { background-color: var(--mico-color-secondary-3xlight) !important; }
.bg-secondary-4xlight { background-color: var(--mico-color-secondary-4xlight) !important; }
.bg-secondary-5xlight { background-color: var(--mico-color-secondary-5xlight) !important; }

/* Secondary Shade Background Colors (darker) */
.bg-secondary-dark { background-color: var(--mico-color-secondary-dark) !important; }
.bg-secondary-2xdark { background-color: var(--mico-color-secondary-2xdark) !important; }
.bg-secondary-3xdark { background-color: var(--mico-color-secondary-3xdark) !important; }
.bg-secondary-4xdark { background-color: var(--mico-color-secondary-4xdark) !important; }
.bg-secondary-5xdark { background-color: var(--mico-color-secondary-5xdark) !important; }

/**
 * Accent Color Variations
 * Shades (darker) and tones (lighter) of the accent brand color
 */

/* Accent Tint Text Colors (lighter) */
.text-accent-ligth { color: var(--mico-color-accent-ligth) !important; }
.text-accent-2xlight { color: var(--mico-color-accent-2xlight) !important; }
.text-accent-3xlight { color: var(--mico-color-accent-3xlight) !important; }
.text-accent-4xlight { color: var(--mico-color-accent-4xlight) !important; }
.text-accent-5xlight { color: var(--mico-color-accent-5xlight) !important; }

/* Accent Shade Text Colors (darker) */
.text-accent-dark { color: var(--mico-color-accent-dark) !important; }
.text-accent-2xdark { color: var(--mico-color-accent-2xdark) !important; }
.text-accent-3xdark { color: var(--mico-color-accent-3xdark) !important; }
.text-accent-4xdark { color: var(--mico-color-accent-4xdark) !important; }
.text-accent-5xdark { color: var(--mico-color-accent-5xdark) !important; }

/* Accent Tint Background Colors (lighter) */
.bg-accent-ligth { background-color: var(--mico-color-accent-ligth) !important; }
.bg-accent-2xlight { background-color: var(--mico-color-accent-2xlight) !important; }
.bg-accent-3xlight { background-color: var(--mico-color-accent-3xlight) !important; }
.bg-accent-4xlight { background-color: var(--mico-color-accent-4xlight) !important; }
.bg-accent-5xlight { background-color: var(--mico-color-accent-5xlight) !important; }

/* Accent Shade Background Colors (darker) */
.bg-accent-dark { background-color: var(--mico-color-accent-dark) !important; }
.bg-accent-2xdark { background-color: var(--mico-color-accent-2xdark) !important; }
.bg-accent-3xdark { background-color: var(--mico-color-accent-3xdark) !important; }
.bg-accent-4xdark { background-color: var(--mico-color-accent-4xdark) !important; }
.bg-accent-5xdark { background-color: var(--mico-color-accent-5xdark) !important; }

/* ====================================================================== */
/* NEUTRAL COLOR UTILITIES                                                */
/* ====================================================================== */

/**
 * Black Color Scale
 * Pure black to lighter black variations for dark themes and high contrast
 */

/* Black Background Colors */
.bg-black-100 { background-color: var(--mico-color-black-100) !important; }
.bg-black-200 { background-color: var(--mico-color-black-200) !important; }
.bg-black-300 { background-color: var(--mico-color-black-300) !important; }
.bg-black-400 { background-color: var(--mico-color-black-400) !important; }
.bg-black-500 { background-color: var(--mico-color-black-500) !important; }
.bg-black-600 { background-color: var(--mico-color-black-600) !important; }
.bg-black-700 { background-color: var(--mico-color-black-700) !important; }
.bg-black-800 { background-color: var(--mico-color-black-800) !important; }
.bg-black-900 { background-color: var(--mico-color-black-900) !important; }

/* Black Text Colors */
.text-black-100 { color: var(--mico-color-black-100) !important; }
.text-black-200 { color: var(--mico-color-black-200) !important; }
.text-black-300 { color: var(--mico-color-black-300) !important; }
.text-black-400 { color: var(--mico-color-black-400) !important; }
.text-black-500 { color: var(--mico-color-black-500) !important; }
.text-black-600 { color: var(--mico-color-black-600) !important; }
.text-black-700 { color: var(--mico-color-black-700) !important; }
.text-black-800 { color: var(--mico-color-black-800) !important; }
.text-black-900 { color: var(--mico-color-black-900) !important; }

/**
 * Gray Color Scale
 * Comprehensive gray palette for UI elements, text, and backgrounds
 * Carefully chosen for optimal contrast and accessibility
 */

/* Gray Background Colors */
.bg-gray-100 { background-color: var(--mico-color-gray-100) !important; }
.bg-gray-200 { background-color: var(--mico-color-gray-200) !important; }
.bg-gray-300 { background-color: var(--mico-color-gray-300) !important; }
.bg-gray-400 { background-color: var(--mico-color-gray-400) !important; }
.bg-gray-500 { background-color: var(--mico-color-gray-500) !important; }
.bg-gray-600 { background-color: var(--mico-color-gray-600) !important; }
.bg-gray-700 { background-color: var(--mico-color-gray-700) !important; }
.bg-gray-800 { background-color: var(--mico-color-gray-800) !important; }
.bg-gray-900 { background-color: var(--mico-color-gray-900) !important; }

/* Gray Text Colors */
.text-gray-100 { color: var(--mico-color-gray-100) !important; }
.text-gray-200 { color: var(--mico-color-gray-200) !important; }
.text-gray-300 { color: var(--mico-color-gray-300) !important; }
.text-gray-400 { color: var(--mico-color-gray-400) !important; }
.text-gray-500 { color: var(--mico-color-gray-500) !important; }
.text-gray-600 { color: var(--mico-color-gray-600) !important; }
.text-gray-700 { color: var(--mico-color-gray-700) !important; }
.text-gray-800 { color: var(--mico-color-gray-800) !important; }
.text-gray-900 { color: var(--mico-color-gray-900) !important; }

/**
 * White Color Scale
 * Pure white to subtle off-white variations for clean, minimal aesthetics
 */

/* White Background Colors */
.bg-white-100 { background-color: var(--mico-color-white-100) !important; }
.bg-white-200 { background-color: var(--mico-color-white-200) !important; }
.bg-white-300 { background-color: var(--mico-color-white-300) !important; }
.bg-white-400 { background-color: var(--mico-color-white-400) !important; }
.bg-white-500 { background-color: var(--mico-color-white-500) !important; }
.bg-white-600 { background-color: var(--mico-color-white-600) !important; }
.bg-white-700 { background-color: var(--mico-color-white-700) !important; }
.bg-white-800 { background-color: var(--mico-color-white-800) !important; }
.bg-white-900 { background-color: var(--mico-color-white-900) !important; }

/* White Text Colors */
.text-white-100 { color: var(--mico-color-white-100) !important; }
.text-white-200 { color: var(--mico-color-white-200) !important; }
.text-white-300 { color: var(--mico-color-white-300) !important; }
.text-white-400 { color: var(--mico-color-white-400) !important; }
.text-white-500 { color: var(--mico-color-white-500) !important; }
.text-white-600 { color: var(--mico-color-white-600) !important; }
.text-white-700 { color: var(--mico-color-white-700) !important; }
.text-white-800 { color: var(--mico-color-white-800) !important; }
.text-white-900 { color: var(--mico-color-white-900) !important; }


/* ====================================================================== */
/* EXTENDED COLOR PALETTE UTILITIES                                       */
/* ====================================================================== */

/**
 * Red Color Scale - Error states, alerts, danger
 * Each color includes 9 variations from light (100) to dark (900)
 */

/* Red Background Colors */
.bg-red-100 { background-color: var(--mico-color-red-100) !important; }
.bg-red-200 { background-color: var(--mico-color-red-200) !important; }
.bg-red-300 { background-color: var(--mico-color-red-300) !important; }
.bg-red-400 { background-color: var(--mico-color-red-400) !important; }
.bg-red-500 { background-color: var(--mico-color-red-500) !important; }
.bg-red-600 { background-color: var(--mico-color-red-600) !important; }
.bg-red-700 { background-color: var(--mico-color-red-700) !important; }
.bg-red-800 { background-color: var(--mico-color-red-800) !important; }
.bg-red-900 { background-color: var(--mico-color-red-900) !important; }

/* Red Text Colors */
.text-red-100 { color: var(--mico-color-red-100) !important; }
.text-red-200 { color: var(--mico-color-red-200) !important; }
.text-red-300 { color: var(--mico-color-red-300) !important; }
.text-red-400 { color: var(--mico-color-red-400) !important; }
.text-red-500 { color: var(--mico-color-red-500) !important; }
.text-red-600 { color: var(--mico-color-red-600) !important; }
.text-red-700 { color: var(--mico-color-red-700) !important; }
.text-red-800 { color: var(--mico-color-red-800) !important; }
.text-red-900 { color: var(--mico-color-red-900) !important; }

/**
 * Yellow Color Scale - Warning states, highlights
 */

/* Yellow Background Colors */
.bg-yellow-100 { background-color: var(--mico-color-yellow-100) !important; }
.bg-yellow-200 { background-color: var(--mico-color-yellow-200) !important; }
.bg-yellow-300 { background-color: var(--mico-color-yellow-300) !important; }
.bg-yellow-400 { background-color: var(--mico-color-yellow-400) !important; }
.bg-yellow-500 { background-color: var(--mico-color-yellow-500) !important; }
.bg-yellow-600 { background-color: var(--mico-color-yellow-600) !important; }
.bg-yellow-700 { background-color: var(--mico-color-yellow-700) !important; }
.bg-yellow-800 { background-color: var(--mico-color-yellow-800) !important; }
.bg-yellow-900 { background-color: var(--mico-color-yellow-900) !important; }

/* Yellow Text Colors */
.text-yellow-100 { color: var(--mico-color-yellow-100) !important; }
.text-yellow-200 { color: var(--mico-color-yellow-200) !important; }
.text-yellow-300 { color: var(--mico-color-yellow-300) !important; }
.text-yellow-400 { color: var(--mico-color-yellow-400) !important; }
.text-yellow-500 { color: var(--mico-color-yellow-500) !important; }
.text-yellow-600 { color: var(--mico-color-yellow-600) !important; }
.text-yellow-700 { color: var(--mico-color-yellow-700) !important; }
.text-yellow-800 { color: var(--mico-color-yellow-800) !important; }
.text-yellow-900 { color: var(--mico-color-yellow-900) !important; }

/**
 * Green Color Scale - Success states, positive actions
 */

/* Green Background Colors */
.bg-green-100 { background-color: var(--mico-color-green-100) !important; }
.bg-green-200 { background-color: var(--mico-color-green-200) !important; }
.bg-green-300 { background-color: var(--mico-color-green-300) !important; }
.bg-green-400 { background-color: var(--mico-color-green-400) !important; }
.bg-green-500 { background-color: var(--mico-color-green-500) !important; }
.bg-green-600 { background-color: var(--mico-color-green-600) !important; }
.bg-green-700 { background-color: var(--mico-color-green-700) !important; }
.bg-green-800 { background-color: var(--mico-color-green-800) !important; }
.bg-green-900 { background-color: var(--mico-color-green-900) !important; }

/* Green Text Colors */
.text-green-100 { color: var(--mico-color-green-100) !important; }
.text-green-200 { color: var(--mico-color-green-200) !important; }
.text-green-300 { color: var(--mico-color-green-300) !important; }
.text-green-400 { color: var(--mico-color-green-400) !important; }
.text-green-500 { color: var(--mico-color-green-500) !important; }
.text-green-600 { color: var(--mico-color-green-600) !important; }
.text-green-700 { color: var(--mico-color-green-700) !important; }
.text-green-800 { color: var(--mico-color-green-800) !important; }
.text-green-900 { color: var(--mico-color-green-900) !important; }

/**
 * Blue Color Scale - Information, links, primary actions
 */

/* Blue Background Colors */
.bg-blue-100 { background-color: var(--mico-color-blue-100) !important; }
.bg-blue-200 { background-color: var(--mico-color-blue-200) !important; }
.bg-blue-300 { background-color: var(--mico-color-blue-300) !important; }
.bg-blue-400 { background-color: var(--mico-color-blue-400) !important; }
.bg-blue-500 { background-color: var(--mico-color-blue-500) !important; }
.bg-blue-600 { background-color: var(--mico-color-blue-600) !important; }
.bg-blue-700 { background-color: var(--mico-color-blue-700) !important; }
.bg-blue-800 { background-color: var(--mico-color-blue-800) !important; }
.bg-blue-900 { background-color: var(--mico-color-blue-900) !important; }

/* Blue Text Colors */
.text-blue-100 { color: var(--mico-color-blue-100) !important; }
.text-blue-200 { color: var(--mico-color-blue-200) !important; }
.text-blue-300 { color: var(--mico-color-blue-300) !important; }
.text-blue-400 { color: var(--mico-color-blue-400) !important; }
.text-blue-500 { color: var(--mico-color-blue-500) !important; }
.text-blue-600 { color: var(--mico-color-blue-600) !important; }
.text-blue-700 { color: var(--mico-color-blue-700) !important; }
.text-blue-800 { color: var(--mico-color-blue-800) !important; }
.text-blue-900 { color: var(--mico-color-blue-900) !important; }

/**
 * Indigo Color Scale - Deep blues, professional themes
 */

/* Indigo Background Colors */
.bg-indigo-100 { background-color: var(--mico-color-indigo-100) !important; }
.bg-indigo-200 { background-color: var(--mico-color-indigo-200) !important; }
.bg-indigo-300 { background-color: var(--mico-color-indigo-300) !important; }
.bg-indigo-400 { background-color: var(--mico-color-indigo-400) !important; }
.bg-indigo-500 { background-color: var(--mico-color-indigo-500) !important; }
.bg-indigo-600 { background-color: var(--mico-color-indigo-600) !important; }
.bg-indigo-700 { background-color: var(--mico-color-indigo-700) !important; }
.bg-indigo-800 { background-color: var(--mico-color-indigo-800) !important; }
.bg-indigo-900 { background-color: var(--mico-color-indigo-900) !important; }

/* Indigo Text Colors */
.text-indigo-100 { color: var(--mico-color-indigo-100) !important; }
.text-indigo-200 { color: var(--mico-color-indigo-200) !important; }
.text-indigo-300 { color: var(--mico-color-indigo-300) !important; }
.text-indigo-400 { color: var(--mico-color-indigo-400) !important; }
.text-indigo-500 { color: var(--mico-color-indigo-500) !important; }
.text-indigo-600 { color: var(--mico-color-indigo-600) !important; }
.text-indigo-700 { color: var(--mico-color-indigo-700) !important; }
.text-indigo-800 { color: var(--mico-color-indigo-800) !important; }
.text-indigo-900 { color: var(--mico-color-indigo-900) !important; }

/**
 * Purple Color Scale - Creative themes, luxury
 */

/* Purple Background Colors */
.bg-purple-100 { background-color: var(--mico-color-purple-100) !important; }
.bg-purple-200 { background-color: var(--mico-color-purple-200) !important; }
.bg-purple-300 { background-color: var(--mico-color-purple-300) !important; }
.bg-purple-400 { background-color: var(--mico-color-purple-400) !important; }
.bg-purple-500 { background-color: var(--mico-color-purple-500) !important; }
.bg-purple-600 { background-color: var(--mico-color-purple-600) !important; }
.bg-purple-700 { background-color: var(--mico-color-purple-700) !important; }
.bg-purple-800 { background-color: var(--mico-color-purple-800) !important; }
.bg-purple-900 { background-color: var(--mico-color-purple-900) !important; }

/* Purple Text Colors */
.text-purple-100 { color: var(--mico-color-purple-100) !important; }
.text-purple-200 { color: var(--mico-color-purple-200) !important; }
.text-purple-300 { color: var(--mico-color-purple-300) !important; }
.text-purple-400 { color: var(--mico-color-purple-400) !important; }
.text-purple-500 { color: var(--mico-color-purple-500) !important; }
.text-purple-600 { color: var(--mico-color-purple-600) !important; }
.text-purple-700 { color: var(--mico-color-purple-700) !important; }
.text-purple-800 { color: var(--mico-color-purple-800) !important; }
.text-purple-900 { color: var(--mico-color-purple-900) !important; }

/**
 * Pink Color Scale - Feminine themes, highlights
 */

/* Pink Background Colors */
.bg-pink-100 { background-color: var(--mico-color-pink-100) !important; }
.bg-pink-200 { background-color: var(--mico-color-pink-200) !important; }
.bg-pink-300 { background-color: var(--mico-color-pink-300) !important; }
.bg-pink-400 { background-color: var(--mico-color-pink-400) !important; }
.bg-pink-500 { background-color: var(--mico-color-pink-500) !important; }
.bg-pink-600 { background-color: var(--mico-color-pink-600) !important; }
.bg-pink-700 { background-color: var(--mico-color-pink-700) !important; }
.bg-pink-800 { background-color: var(--mico-color-pink-800) !important; }
.bg-pink-900 { background-color: var(--mico-color-pink-900) !important; }

/* Pink Text Colors */
.text-pink-100 { color: var(--mico-color-pink-100) !important; }
.text-pink-200 { color: var(--mico-color-pink-200) !important; }
.text-pink-300 { color: var(--mico-color-pink-300) !important; }
.text-pink-400 { color: var(--mico-color-pink-400) !important; }
.text-pink-500 { color: var(--mico-color-pink-500) !important; }
.text-pink-600 { color: var(--mico-color-pink-600) !important; }
.text-pink-700 { color: var(--mico-color-pink-700) !important; }
.text-pink-800 { color: var(--mico-color-pink-800) !important; }
.text-pink-900 { color: var(--mico-color-pink-900) !important; }

/* ====================================================================== */
/* TRANSPARENCY COLOR UTILITIES                                           */
/* ====================================================================== */

/**
 * Semi-transparent overlays and backgrounds for modals, dropdowns, and effects
 * These maintain consistent opacity levels across the framework
 */

/* Black Transparency Background Colors */
.bg-black-trans-100 { background-color: var(--mico-color-black-trans-100) !important; }
.bg-black-trans-200 { background-color: var(--mico-color-black-trans-200) !important; }
.bg-black-trans-300 { background-color: var(--mico-color-black-trans-300) !important; }
.bg-black-trans-400 { background-color: var(--mico-color-black-trans-400) !important; }
.bg-black-trans-500 { background-color: var(--mico-color-black-trans-500) !important; }
.bg-black-trans-600 { background-color: var(--mico-color-black-trans-600) !important; }
.bg-black-trans-700 { background-color: var(--mico-color-black-trans-700) !important; }
.bg-black-trans-800 { background-color: var(--mico-color-black-trans-800) !important; }
.bg-black-trans-900 { background-color: var(--mico-color-black-trans-900) !important; }

/* Gray Transparency Background Colors */
.bg-gray-trans-100 { background-color: var(--mico-color-gray-trans-100) !important; }
.bg-gray-trans-200 { background-color: var(--mico-color-gray-trans-200) !important; }
.bg-gray-trans-300 { background-color: var(--mico-color-gray-trans-300) !important; }
.bg-gray-trans-400 { background-color: var(--mico-color-gray-trans-400) !important; }
.bg-gray-trans-500 { background-color: var(--mico-color-gray-trans-500) !important; }
.bg-gray-trans-600 { background-color: var(--mico-color-gray-trans-600) !important; }
.bg-gray-trans-700 { background-color: var(--mico-color-gray-trans-700) !important; }
.bg-gray-trans-800 { background-color: var(--mico-color-gray-trans-800) !important; }
.bg-gray-trans-900 { background-color: var(--mico-color-gray-trans-900) !important; }

/* White Transparency Background Colors */
.bg-white-trans-100 { background-color: var(--mico-color-white-trans-100) !important; }
.bg-white-trans-200 { background-color: var(--mico-color-white-trans-200) !important; }
.bg-white-trans-300 { background-color: var(--mico-color-white-trans-300) !important; }
.bg-white-trans-400 { background-color: var(--mico-color-white-trans-400) !important; }
.bg-white-trans-500 { background-color: var(--mico-color-white-trans-500) !important; }
.bg-white-trans-600 { background-color: var(--mico-color-white-trans-600) !important; }
.bg-white-trans-700 { background-color: var(--mico-color-white-trans-700) !important; }
.bg-white-trans-800 { background-color: var(--mico-color-white-trans-800) !important; }
.bg-white-trans-900 { background-color: var(--mico-color-white-trans-900) !important; }

/* ====================================================================== */
/* SEMANTIC COLOR UTILITIES                                               */
/* ====================================================================== */

/**
 * Colors that convey meaning and state information
 * These maintain WCAG AA contrast ratios for accessibility
 */

/* Semantic Background Colors */
.bg-success { background-color: var(--mico-color-success) !important; }
.bg-warning { background-color: var(--mico-color-warning) !important; }
.bg-error { background-color: var(--mico-color-error) !important; }
.bg-info { background-color: var(--mico-color-info) !important; }
.bg-visited { background-color: var(--mico-color-visited) !important; }

/* Semantic Text Colors */
.text-success { color: var(--mico-color-success) !important; }
.text-warning { color: var(--mico-color-warning) !important; }
.text-error { color: var(--mico-color-error) !important; }
.text-info { color: var(--mico-color-info) !important; }
.text-visited { color: var(--mico-color-visited) !important; }
