:root{--mico-breakpoint-xs:490px;--mico-breakpoint-sm:576px;--mico-breakpoint-md:768px;--mico-breakpoint-lg:992px;--mico-breakpoint-xl:1280px;--mico-breakpoint-2xl:1440px;--mico-breakpoint-3xl:1600px;--mico-value-auto:auto;--mico-value-none:none;--mico-value-normal:normal;--mico-value-inherit:inherit;--mico-value-initial:initial;--mico-value-unset:unset;--mico-value-current:currentColor;--mico-value-transparent:transparent;--mico-value-0:0;--mico-font-sans:system-ui,-apple-system,Segoe UI,Roboto,Ubuntu,Cantarell,Noto Sans,sans-serif,"Segoe UI",Roboto,Helvetica,Arial,sans-serif;--mico-font-serif:Georgia,Cambria,"Times New Roman",Times,serif;--mico-font-mono:"SFMono-Regular",<PERSON><PERSON>,Monaco,Consolas,"Liberation Mono","Courier New",monospace;--mico-font-display:var(--mico-font-sans);--mico-font-body:var(--mico-font-sans);--mico-fs-xs:max(0.75rem,min(1.5vw,0.875rem));--mico-fs-sm:max(0.875rem,min(2vw,1rem));--mico-fs-md:max(1rem,min(2.5vw,1.125rem));--mico-fs-lg:max(1.25rem,min(3vw,1.875rem));--mico-fs-xl:max(1.5rem,min(4vw,2.25rem));--mico-fs-2xl:max(1.875rem,min(5vw,3rem));--mico-fs-3xl:max(2.25rem,min(5.5vw,3.5rem));--mico-fs-4xl:max(2.5rem,min(6vw,4rem));--mico-fs-5xl:max(3rem,min(6.5vw,4.5rem));--mico-fs-6xl:max(3.5rem,min(7vw,5rem));--mico-fs-7xl:max(3.75rem,min(7.5vw,5.5rem));--mico-fs-8xl:max(4rem,min(8vw,6rem));--mico-fs-9xl:max(4.5rem,min(9vw,7rem));--mico-fw-100:100;--mico-fw-200:200;--mico-fw-300:300;--mico-fw-400:400;--mico-fw-500:500;--mico-fw-600:600;--mico-fw-700:700;--mico-fw-800:800;--mico-fw-900:900;--mico-font-stretch-ultra-condensed:ultra-condensed;--mico-font-stretch-extra-condensed:extra-condensed;--mico-font-stretch-condensed:condensed;--mico-font-stretch-semi-condensed:semi-condensed;--mico-font-stretch-normal:var(--mico-value-normal);--mico-font-stretch-semi-expanded:semi-expanded;--mico-font-stretch-expanded:expanded;--mico-font-stretch-extra-expanded:extra-expanded;--mico-font-stretch-ultra-expanded:ultra-expanded;--mico-font-style-normal:var(--mico-value-normal);--mico-font-style-italic:italic;--mico-font-variant-numeric-normal:var(--mico-value-normal);--mico-font-variant-numeric-ordinal:ordinal;--mico-font-variant-numeric-slashed-zero:slashed-zero;--mico-font-variant-numeric-lining-nums:lining-nums;--mico-font-variant-numeric-oldstyle-nums:oldstyle-nums;--mico-font-variant-numeric-proportional-nums:proportional-nums;--mico-font-variant-numeric-tabular-nums:tabular-nums;--mico-font-variant-numeric-diagonal-fractions:diagonal-fractions;--mico-font-variant-numeric-stacked-fractions:stacked-fractions;--mico-font-variant-ligatures-common:common-ligatures;--mico-font-variant-ligatures-no-common:no-common-ligatures;--mico-font-variant-ligatures-discretionary:discretionary-ligatures;--mico-font-variant-ligatures-no-discretionary:no-discretionary-ligatures;--mico-font-variant-ligatures-historical:historical-ligatures;--mico-font-variant-ligatures-no-historical:no-historical-ligatures;--mico-font-variant-ligatures-contextual:contextual;--mico-font-variant-ligatures-no-contextual:no-contextual;--mico-font-variant-caps-normal:var(--mico-value-normal);--mico-font-variant-caps-small-caps:small-caps;--mico-font-variant-caps-all-small-caps:all-small-caps;--mico-font-variant-caps-petite-caps:petite-caps;--mico-font-variant-caps-all-petite-caps:all-petite-caps;--mico-font-variant-caps-unicase:unicase;--mico-font-variant-caps-titling-caps:titling-caps;--mico-lh-xs:1;--mico-lh-sm:1.25;--mico-lh-md:1.5;--mico-lh-lg:1.625;--mico-lh-xl:2;--mico-lh-2xl:0.75rem;--mico-lh-3xl:1rem;--mico-lh-4xl:1.25rem;--mico-lh-5xl:1.5rem;--mico-lh-6xl:1.75rem;--mico-lh-7xl:2rem;--mico-lh-8xl:2.25rem;--mico-lh-9xl:2.5rem;--mico-ls-xs:-0.05em;--mico-ls-sm:-0.025em;--mico-ls-md:0em;--mico-ls-lg:0.025em;--mico-ls-xl:0.05em;--mico-ls-2xl:0.1em;--mico-underline-offset-auto:var(--mico-value-auto);--mico-underline-offset-0:var(--mico-value-0);--mico-underline-offset-1:1px;--mico-underline-offset-2:2px;--mico-underline-offset-4:4px;--mico-underline-offset-8:8px;--mico-underline-offset:0.15em;--mico-decoration-thickness-auto:var(--mico-value-auto);--mico-decoration-thickness-from-font:from-font;--mico-decoration-thickness-0:0px;--mico-decoration-thickness-1:1px;--mico-decoration-thickness-2:2px;--mico-decoration-thickness-4:4px;--mico-decoration-thickness-8:8px;--mico-underline-thickness:0.05em;--mico-decoration-style-solid:solid;--mico-decoration-style-double:double;--mico-decoration-style-dotted:dotted;--mico-decoration-style-dashed:dashed;--mico-decoration-style-wavy:wavy;--mico-underline-position-auto:var(--mico-value-auto);--mico-underline-position-under:under;--mico-underline-position-left:left;--mico-underline-position-right:right;--mico-text-transform-uppercase:uppercase;--mico-text-transform-lowercase:lowercase;--mico-text-transform-capitalize:capitalize;--mico-text-transform-none:var(--mico-value-none);--mico-text-align-left:left;--mico-text-align-center:center;--mico-text-align-right:right;--mico-text-align-justify:justify;--mico-text-align-start:start;--mico-text-align-end:end;--mico-text-overflow-ellipsis:ellipsis;--mico-text-overflow-clip:clip;--mico-whitespace-normal:var(--mico-value-normal);--mico-whitespace-nowrap:nowrap;--mico-whitespace-pre:pre;--mico-whitespace-pre-line:pre-line;--mico-whitespace-pre-wrap:pre-wrap;--mico-whitespace-break-spaces:break-spaces;--mico-indent-0:var(--mico-value-0);--mico-indent-xs:1px;--mico-indent-sm:0.25rem;--mico-indent-md:0.5rem;--mico-indent-lg:1rem;--mico-indent-xl:2rem;--mico-text-shadow-none:var(--mico-value-none);--mico-text-shadow-xs:1px 1px 2px rgba(0,0,0,.1);--mico-text-shadow-sm:2px 2px 4px rgba(0,0,0,.1);--mico-text-shadow-md:4px 4px 6px rgba(0,0,0,.1);--mico-text-shadow-lg:6px 6px 8px rgba(0,0,0,.15);--mico-text-stroke-xs:1px;--mico-text-stroke-sm:2px;--mico-text-stroke-md:4px;--mico-list-style-type-none:var(--mico-value-none);--mico-list-style-type-disc:disc;--mico-list-style-type-decimal:decimal;--mico-list-style-type-square:square;--mico-list-style-type-upper-roman:upper-roman;--mico-list-style-type-lower-roman:lower-roman;--mico-list-style-type-upper-alpha:upper-alpha;--mico-list-style-type-lower-alpha:lower-alpha;--mico-list-style-position-inside:inside;--mico-list-style-position-outside:outside;--mico-text-direction-ltr:ltr;--mico-text-direction-rtl:rtl;--mico-writing-mode-horizontal-tb:horizontal-tb;--mico-writing-mode-vertical-rl:vertical-rl;--mico-writing-mode-vertical-lr:vertical-lr;--mico-text-orientation-mixed:mixed;--mico-text-orientation-upright:upright;--mico-text-orientation-sideways:sideways;--mico-hyphens-none:var(--mico-value-none);--mico-hyphens-manual:manual;--mico-hyphens-auto:var(--mico-value-auto);--mico-text-align-last-auto:var(--mico-value-auto);--mico-text-align-last-start:start;--mico-text-align-last-end:end;--mico-text-align-last-left:left;--mico-text-align-last-right:right;--mico-text-align-last-center:center;--mico-text-align-last-justify:justify;--mico-text-justify-auto:var(--mico-value-auto);--mico-text-justify-inter-word:inter-word;--mico-text-justify-inter-character:inter-character;--mico-text-justify-none:var(--mico-value-none);--mico-user-select-none:var(--mico-value-none);--mico-user-select-text:text;--mico-user-select-all:all;--mico-user-select-auto:var(--mico-value-auto);--mico-word-break-normal:var(--mico-value-normal);--mico-word-break-break-all:break-all;--mico-word-break-keep-all:keep-all;--mico-overflow-wrap-normal:var(--mico-value-normal);--mico-overflow-wrap-break-word:break-word;--mico-size-unit:4px;--mico-size-0:0;--mico-size-1:1px;--mico-size-2:2px;--mico-size-3:3px;--mico-size-4:calc(var(--mico-size-unit) * 1);--mico-size-6:6px;--mico-size-8:calc(var(--mico-size-unit) * 2);--mico-size-10:10px;--mico-size-12:calc(var(--mico-size-unit) * 3);--mico-size-14:14px;--mico-size-16:calc(var(--mico-size-unit) * 4);--mico-size-18:18px;--mico-size-20:calc(var(--mico-size-unit) * 5);--mico-size-22:22px;--mico-size-24:calc(var(--mico-size-unit) * 6);--mico-size-28:calc(var(--mico-size-unit) * 7);--mico-size-32:calc(var(--mico-size-unit) * 8);--mico-size-36:calc(var(--mico-size-unit) * 9);--mico-size-40:calc(var(--mico-size-unit) * 10);--mico-size-44:calc(var(--mico-size-unit) * 11);--mico-size-48:calc(var(--mico-size-unit) * 12);--mico-size-52:calc(var(--mico-size-unit) * 13);--mico-size-56:calc(var(--mico-size-unit) * 14);--mico-size-60:calc(var(--mico-size-unit) * 15);--mico-size-64:calc(var(--mico-size-unit) * 16);--mico-size-72:calc(var(--mico-size-unit) * 18);--mico-size-80:calc(var(--mico-size-unit) * 20);--mico-size-96:calc(var(--mico-size-unit) * 24);--mico-size-100:calc(var(--mico-size-unit) * 25);--mico-size-112:calc(var(--mico-size-unit) * 28);--mico-size-128:calc(var(--mico-size-unit) * 32);--mico-size-144:calc(var(--mico-size-unit) * 36);--mico-size-160:calc(var(--mico-size-unit) * 40);--mico-size-176:calc(var(--mico-size-unit) * 44);--mico-size-192:calc(var(--mico-size-unit) * 48);--mico-size-208:calc(var(--mico-size-unit) * 52);--mico-size-224:calc(var(--mico-size-unit) * 56);--mico-size-240:calc(var(--mico-size-unit) * 60);--mico-size-256:calc(var(--mico-size-unit) * 64);--mico-size-fluid-xs:max(var(--mico-size-16),min(3vw,var(--mico-size-32)));--mico-size-fluid-sm:max(var(--mico-size-16),min(4vw,var(--mico-size-56)));--mico-size-fluid-md:max(var(--mico-size-16),min(6vw,var(--mico-size-80)));--mico-size-fluid-lg:max(var(--mico-size-16),min(8vw,var(--mico-size-100)));--mico-size-fluid-xl:max(var(--mico-size-16),min(10vw,var(--mico-size-128)));--mico-size-fluid-2xl:max(var(--mico-size-16),min(12vw,var(--mico-size-192)));--mico-radius-none:0;--mico-radius-xs:1px;--mico-radius-sm:2px;--mico-radius-md:4px;--mico-radius-lg:8px;--mico-radius-xl:12px;--mico-radius-2xl:16px;--mico-radius-full:9999px;--mico-border-none:none;--mico-border-solid:solid;--mico-border-dashed:dashed;--mico-border-dotted:dotted;--mico-border-double:double;--mico-border-groove:groove;--mico-border-ridge:ridge;--mico-border-inset:inset;--mico-border-outset:outset;--mico-border-width-0:0px;--mico-border-width-1:1px;--mico-border-width-2:2px;--mico-border-width-4:4px;--mico-border-width-8:8px;--mico-outline-width-0:0px;--mico-outline-width-1:1px;--mico-outline-width-2:2px;--mico-outline-width-4:4px;--mico-outline-width-8:8px;--mico-outline-style-none:none;--mico-outline-style-solid:solid;--mico-outline-style-dashed:dashed;--mico-outline-style-dotted:dotted;--mico-outline-style-double:double;--mico-outline-offset-0:0px;--mico-outline-offset-1:1px;--mico-outline-offset-2:2px;--mico-outline-offset-4:4px;--mico-outline-offset-8:8px;--mico-ring-width-0:0px;--mico-ring-width-1:1px;--mico-ring-width-2:2px;--mico-ring-width-4:4px;--mico-ring-width-8:8px;--mico-ring-offset-width-0:0px;--mico-ring-offset-width-1:1px;--mico-ring-offset-width-2:2px;--mico-ring-offset-width-4:4px;--mico-ring-offset-width-8:8px;--mico-divide-width-0:0px;--mico-divide-width-1:1px;--mico-divide-width-2:2px;--mico-divide-width-4:4px;--mico-divide-width-8:8px;--mico-shadow-sm-light:0 1px 2px rgba(0,0,0,.05);--mico-shadow-md-light:0 4px 6px rgba(0,0,0,.1);--mico-shadow-lg-light:0 10px 15px rgba(0,0,0,.1);--mico-shadow-xl-light:0 20px 25px rgba(0,0,0,.15);--mico-shadow-sm-dark:0 1px 2px hsla(0,0%,100%,.05);--mico-shadow-md-dark:0 4px 6px hsla(0,0%,100%,.1);--mico-shadow-lg-dark:0 10px 15px hsla(0,0%,100%,.1);--mico-shadow-xl-dark:0 20px 25px hsla(0,0%,100%,.15);--mico-shadow-sm:var(--mico-shadow-sm-light);--mico-shadow-md:var(--mico-shadow-md-light);--mico-shadow-lg:var(--mico-shadow-lg-light);--mico-shadow-xl:var(--mico-shadow-xl-light);--mico-shadow-inset-sm:inset 0 1px 2px rgba(0,0,0,.05);--mico-shadow-inset-md:inset 0 4px 6px rgba(0,0,0,.1);--mico-shadow-focus:0 0 0 3px rgba(66,153,225,.5);--mico-position-static:static;--mico-position-relative:relative;--mico-position-absolute:absolute;--mico-position-fixed:fixed;--mico-position-sticky:sticky;--mico-display-block:block;--mico-display-inline:inline;--mico-display-inline-block:inline-block;--mico-display-flex:flex;--mico-display-inline-flex:inline-flex;--mico-display-grid:grid;--mico-display-none:none;--mico-box-sizing-border:border-box;--mico-box-sizing-content:content-box;--mico-box-decoration-slice:slice;--mico-box-decoration-clone:clone;--mico-overflow-auto:auto;--mico-overflow-hidden:hidden;--mico-overflow-visible:visible;--mico-overflow-scroll:scroll;--mico-overscroll-auto:auto;--mico-overscroll-contain:contain;--mico-overscroll-none:none;--mico-aspect-ratio-square:1/1;--mico-aspect-ratio-video:16/9;--mico-aspect-ratio-portrait:3/4;--mico-aspect-ratio-landscape:4/3;--mico-aspect-ratio-widescreen:21/9;--mico-aspect-ratio-golden:1.618/1;--mico-float-left:left;--mico-float-right:right;--mico-float-none:none;--mico-clear-left:left;--mico-clear-right:right;--mico-clear-both:both;--mico-object-fit-contain:contain;--mico-object-fit-cover:cover;--mico-object-fit-fill:fill;--mico-object-fit-scale-down:scale-down;--mico-object-position-center:center;--mico-visibility-visible:visible;--mico-visibility-hidden:hidden;--mico-visibility-collapse:collapse;--mico-isolation-isolate:isolate;--mico-isolation-auto:auto;--mico-inset-0:0;--mico-inset-auto:auto;--mico-flex-row:row;--mico-flex-row-reverse:row-reverse;--mico-flex-col:column;--mico-flex-col-reverse:column-reverse;--mico-flex-wrap:wrap;--mico-flex-nowrap:nowrap;--mico-flex-wrap-reverse:wrap-reverse;--mico-justify-start:flex-start;--mico-justify-end:flex-end;--mico-justify-center:center;--mico-justify-between:space-between;--mico-justify-around:space-around;--mico-justify-evenly:space-evenly;--mico-items-start:flex-start;--mico-items-end:flex-end;--mico-items-center:center;--mico-items-baseline:baseline;--mico-items-stretch:stretch;--mico-grid-auto-fit:auto-fit;--mico-grid-auto-fill:auto-fill;--mico-place-items-start:start;--mico-place-items-end:end;--mico-place-items-center:center;--mico-place-items-stretch:stretch;--mico-place-content-start:start;--mico-place-content-end:end;--mico-place-content-center:center;--mico-place-content-stretch:stretch;--mico-place-content-around:space-around;--mico-place-content-between:space-between;--mico-place-content-evenly:space-evenly;--mico-grid-column-count:12;--mico-grid-min-column-width:200px;--mico-grid-row-count:1;--mico-grid-min-row-height:100px;--mico-column-span:1;--mico-row-span:1;--mico-min-column-width:0;--mico-max-column-width:1fr;--mico-min-row-height:0;--mico-max-row-height:1fr;--mico-grid-cols:repeat(var(--mico-grid-column-count,12),minmax(0,1fr));--mico-grid-cols-auto-fit:repeat(auto-fit,minmax(var(--mico-grid-min-column-width,200px),1fr));--mico-grid-rows:repeat(var(--mico-grid-row-count,1),minmax(0,1fr));--mico-col-span:span var(--mico-column-span,1);--mico-row-span:span var(--mico-row-span,1);--mico-grid-flow-row:row;--mico-grid-flow-col:column;--mico-grid-flow-dense:dense;--mico-auto-cols:minmax(var(--mico-min-column-width,0),var(--mico-max-column-width,1fr));--mico-auto-rows:minmax(var(--mico-min-row-height,0),var(--mico-max-row-height,1fr));--mico-gap-xs:var(--mico-size-4);--mico-gap-sm:var(--mico-size-8);--mico-gap-md:var(--mico-size-16);--mico-gap-lg:var(--mico-size-24);--mico-gap-xl:var(--mico-size-32);--mico-gap:var(--mico-gap-md);--mico-row-gap:var(--mico-gap);--mico-column-gap:var(--mico-gap);--mico-bg-none:none;--mico-bg-repeat:repeat;--mico-bg-no-repeat:no-repeat;--mico-bg-repeat-x:repeat-x;--mico-bg-repeat-y:repeat-y;--mico-bg-fixed:fixed;--mico-bg-local:local;--mico-bg-scroll:scroll;--mico-bg-clip-border:border-box;--mico-bg-clip-padding:padding-box;--mico-bg-clip-content:content-box;--mico-filter-blur:blur(8px);--mico-filter-brightness:brightness(1.5);--mico-filter-contrast:contrast(1.2);--mico-filter-grayscale:grayscale(100%);--mico-filter-hue-rotate:hue-rotate(90deg);--mico-filter-invert:invert(100%);--mico-filter-saturate:saturate(2);--mico-filter-sepia:sepia(100%);--mico-opacity-0:0;--mico-opacity-25:0.25;--mico-opacity-50:0.5;--mico-opacity-75:0.75;--mico-opacity-100:1;--mico-scale-100:scale(1);--mico-scale-75:scale(0.75);--mico-scale-50:scale(0.5);--mico-rotate-45:rotate(45deg);--mico-rotate-90:rotate(90deg);--mico-translate-x-full:translateX(100%);--mico-translate-y-full:translateY(100%);--mico-table-auto:auto;--mico-table-fixed:fixed;--mico-fill-current:currentColor;--mico-stroke-current:currentColor;--mico-btn-padding-xs:var(--mico-size-8) var(--mico-size-12);--mico-btn-padding-sm:var(--mico-size-12) var(--mico-size-16);--mico-btn-padding-md:var(--mico-size-16) var(--mico-size-24);--mico-btn-padding-lg:var(--mico-size-20) var(--mico-size-32);--mico-btn-padding-xl:var(--mico-size-24) var(--mico-size-40);--mico-btn-font-size-xs:var(--mico-fs-xs);--mico-btn-font-size-sm:var(--mico-fs-sm);--mico-btn-font-size-md:var(--mico-fs-md);--mico-btn-font-size-lg:var(--mico-fs-lg);--mico-btn-font-size-xl:var(--mico-fs-xl);--mico-btn-radius-square:var(--mico-radius-none);--mico-btn-radius-sm:var(--mico-radius-sm);--mico-btn-radius-md:var(--mico-radius-md);--mico-btn-radius-lg:var(--mico-radius-lg);--mico-btn-radius-pill:var(--mico-radius-full);--mico-btn-radius-circle:var(--mico-radius-full);--mico-btn-shadow-none:none;--mico-btn-shadow-sm:var(--mico-shadow-sm);--mico-btn-shadow-md:var(--mico-shadow-md);--mico-btn-shadow-lg:var(--mico-shadow-lg);--mico-btn-icon-gap:var(--mico-size-8);--mico-btn-icon-only-size:var(--mico-size-40);--mico-cursor-auto:auto;--mico-cursor-default:default;--mico-cursor-pointer:pointer;--mico-cursor-wait:wait;--mico-cursor-text:text;--mico-cursor-move:move;--mico-cursor-not-allowed:not-allowed;--mico-cursor-grab:grab;--mico-cursor-grabbing:grabbing;--mico-cursor-help:help;--mico-appearance-none:none;--mico-appearance-auto:auto;--mico-pointer-events-none:none;--mico-pointer-events-auto:auto;--mico-will-change-auto:auto;--mico-will-change-transform:transform;--mico-will-change-opacity:opacity;--mico-will-change-scroll:scroll-position;--mico-bleed-offset-sm:10vw;--mico-bleed-offset-md:20vw;--mico-bleed-offset-lg:30vw;--mico-mask-fade-to-top:linear-gradient(0deg,#000 50%,transparent);--mico-mask-fade-to-bottom:linear-gradient(180deg,#000 50%,transparent);--mico-mask-fade-to-left:linear-gradient(270deg,#000 50%,transparent);--mico-mask-fade-to-right:linear-gradient(90deg,#000 50%,transparent);--mico-mask-fade-short:linear-gradient(180deg,#000 80%,transparent);--mico-mask-fade-long:linear-gradient(180deg,#000 20%,transparent);--mico-fit-content:fit-content;--mico-min-content:min-content;--mico-max-content:max-content;--mico-width-full:100%;--mico-height-full:100%;--mico-width-half:50%;--mico-height-half:50%;--mico-width-quarter:25%;--mico-height-quarter:25%;--mico-width-third:33.33%;--mico-height-third:33.33%;--mico-width-screen:100vw;--mico-height-screen:100vh;--mico-min-width-0:0;--mico-min-height-0:0;--mico-max-width-full:100%;--mico-max-height-full:100%;--mico-align-baseline:baseline;--mico-align-top:top;--mico-align-middle:middle;--mico-align-bottom:bottom;--mico-align-text-top:text-top;--mico-align-text-bottom:text-bottom;--mico-vertical-align-sub:sub;--mico-vertical-align-super:super;--mico-duration-xs:0.2s;--mico-duration-sm:0.5s;--mico-duration-md:0.8s;--mico-duration-lg:1.2s;--mico-duration-xl:2s;--mico-delay-xs:0.1s;--mico-delay-sm:0.2s;--mico-delay-md:0.4s;--mico-delay-lg:0.6s;--mico-delay-xl:0.8s;--mico-anim-default-duration:var(--mico-duration-md);--mico-anim-default-timing:ease-out;--mico-anim-default-fill-mode:both;--mico-ripple-bg-default:hsla(0,0%,100%,.3);--mico-ripple-bg-dark:rgba(0,0,0,.2);--mico-typewriter-cursor-color:currentColor;--mico-animation-none:none;--mico-animation-spin:spin 1s linear infinite;--mico-animation-ping:ping 1s cubic-bezier(0,0,0.2,1) infinite;--mico-animation-pulse:pulse 2s cubic-bezier(0.4,0,0.6,1) infinite;--mico-animation-bounce:bounce 1s infinite;--mico-ease:cubic-bezier(0.25,0.1,0.25,1.0);--mico-ease-in:cubic-bezier(0.42,0,1.0,1.0);--mico-ease-out:cubic-bezier(0,0,0.58,1.0);--mico-ease-in-out:cubic-bezier(0.42,0,0.58,1.0);--mico-ease-elastic:cubic-bezier(0.68,-0.55,0.265,1.55);--mico-ease-bounce:cubic-bezier(0.175,0.885,0.32,1.275);--mico-ease-back:cubic-bezier(0.68,-0.55,0.265,1.55);--mico-ease-spring:cubic-bezier(0.5,0.1,0.1,1);--mico-ease-gravity:cubic-bezier(0.175,0.885,0.32,1.275);--mico-ease-snappy:cubic-bezier(0.1,0.9,0.2,1);--mico-transition-duration-fast:150ms;--mico-transition-duration-normal:300ms;--mico-transition-duration-slow:500ms;--mico-transition-all:all .4s var(--mico-ease);--mico-transition-color:color .4s var(--mico-ease);--mico-transition-background:background .4s var(--mico-ease);--mico-transition-border:border .4s var(--mico-ease);--mico-transition-opacity:opacity .4s var(--mico-ease);--mico-transition-transform:transform .4s var(--mico-ease);--mico-transition-box-shadow:box-shadow .4s var(--mico-ease);--mico-color-primary:#3b82f6;--mico-color-secondary:#6b7280;--mico-color-accent:#f59e0b;--mico-color-primary-light:hsl(from var(--mico-color-primary) h s calc(l + (100% - l) * 0.2));--mico-color-primary-2xlight:hsl(from var(--mico-color-primary) h s calc(l + (100% - l) * 0.4));--mico-color-primary-3xlight:hsl(from var(--mico-color-primary) h s calc(l + (100% - l) * 0.6));--mico-color-primary-4xlight:hsl(from var(--mico-color-primary) h s calc(l + (100% - l) * 0.8));--mico-color-primary-5xlight:hsl(from var(--mico-color-primary) h s calc(l + (100% - l) * 0.9));--mico-color-primary-dark:hsl(from var(--mico-color-primary) h s calc(l * 0.8));--mico-color-primary-2xdark:hsl(from var(--mico-color-primary) h s calc(l * 0.6));--mico-color-primary-3xdark:hsl(from var(--mico-color-primary) h s calc(l * 0.4));--mico-color-primary-4xdark:hsl(from var(--mico-color-primary) h s calc(l * 0.2));--mico-color-primary-5xdark:hsl(from var(--mico-color-primary) h s calc(l * 0.1));--mico-color-secondary-light:hsl(from var(--mico-color-secondary) h s calc(l + (100% - l) * 0.2));--mico-color-secondary-2xlight:hsl(from var(--mico-color-secondary) h s calc(l + (100% - l) * 0.4));--mico-color-secondary-3xlight:hsl(from var(--mico-color-secondary) h s calc(l + (100% - l) * 0.6));--mico-color-secondary-4xlight:hsl(from var(--mico-color-secondary) h s calc(l + (100% - l) * 0.8));--mico-color-secondary-5xlight:hsl(from var(--mico-color-secondary) h s calc(l + (100% - l) * 0.9));--mico-color-secondary-dark:hsl(from var(--mico-color-secondary) h s calc(l * 0.8));--mico-color-secondary-2xdark:hsl(from var(--mico-color-secondary) h s calc(l * 0.6));--mico-color-secondary-3xdark:hsl(from var(--mico-color-secondary) h s calc(l * 0.4));--mico-color-secondary-4xdark:hsl(from var(--mico-color-secondary) h s calc(l * 0.2));--mico-color-secondary-5xdark:hsl(from var(--mico-color-secondary) h s calc(l * 0.1));--mico-color-accent-light:hsl(from var(--mico-color-accent) h s calc(l + (100% - l) * 0.2));--mico-color-accent-2xlight:hsl(from var(--mico-color-accent) h s calc(l + (100% - l) * 0.4));--mico-color-accent-3xlight:hsl(from var(--mico-color-accent) h s calc(l + (100% - l) * 0.6));--mico-color-accent-4xlight:hsl(from var(--mico-color-accent) h s calc(l + (100% - l) * 0.8));--mico-color-accent-5xlight:hsl(from var(--mico-color-accent) h s calc(l + (100% - l) * 0.9));--mico-color-accent-dark:hsl(from var(--mico-color-accent) h s calc(l * 0.8));--mico-color-accent-2xdark:hsl(from var(--mico-color-accent) h s calc(l * 0.6));--mico-color-accent-3xdark:hsl(from var(--mico-color-accent) h s calc(l * 0.4));--mico-color-accent-4xdark:hsl(from var(--mico-color-accent) h s calc(l * 0.2));--mico-color-accent-5xdark:hsl(from var(--mico-color-accent) h s calc(l * 0.1));--mico-color-success:#10b981;--mico-color-warning:#f59e0b;--mico-color-error:#ef4444;--mico-color-info:#3b82f6;--mico-color-visited:#8b5cf6;--mico-color-black-100:#000;--mico-color-black-200:#0d0d0d;--mico-color-black-300:#1a1a1a;--mico-color-black-400:#262626;--mico-color-black-500:#333;--mico-color-black-600:#404040;--mico-color-black-700:#4d4d4d;--mico-color-black-800:#595959;--mico-color-black-900:#666;--mico-color-gray-100:#f3f4f6;--mico-color-gray-200:#e5e7eb;--mico-color-gray-300:#d1d5db;--mico-color-gray-400:#9ca3af;--mico-color-gray-500:#6b7280;--mico-color-gray-600:#4b5563;--mico-color-gray-700:#374151;--mico-color-gray-800:#1f2937;--mico-color-gray-900:#111827;--mico-color-white-100:#fff;--mico-color-white-200:#fafafa;--mico-color-white-300:#f5f5f5;--mico-color-white-400:#f0f0f0;--mico-color-white-500:#ebebeb;--mico-color-white-600:#e6e6e6;--mico-color-white-700:#e0e0e0;--mico-color-white-800:#dbdbdb;--mico-color-white-900:#d6d6d6;--mico-color-black-trans-100:rgba(0,0,0,.1);--mico-color-black-trans-200:rgba(0,0,0,.2);--mico-color-black-trans-300:rgba(0,0,0,.3);--mico-color-black-trans-400:rgba(0,0,0,.4);--mico-color-black-trans-500:rgba(0,0,0,.5);--mico-color-black-trans-600:rgba(0,0,0,.6);--mico-color-black-trans-700:rgba(0,0,0,.7);--mico-color-black-trans-800:rgba(0,0,0,.8);--mico-color-black-trans-900:rgba(0,0,0,.9);--mico-color-gray-trans-100:hsla(220,9%,46%,.1);--mico-color-gray-trans-200:hsla(220,9%,46%,.2);--mico-color-gray-trans-300:hsla(220,9%,46%,.3);--mico-color-gray-trans-400:hsla(220,9%,46%,.4);--mico-color-gray-trans-500:hsla(220,9%,46%,.5);--mico-color-gray-trans-600:hsla(220,9%,46%,.6);--mico-color-gray-trans-700:hsla(220,9%,46%,.7);--mico-color-gray-trans-800:hsla(220,9%,46%,.8);--mico-color-gray-trans-900:hsla(220,9%,46%,.9);--mico-color-white-trans-100:hsla(0,0%,100%,.1);--mico-color-white-trans-200:hsla(0,0%,100%,.2);--mico-color-white-trans-300:hsla(0,0%,100%,.3);--mico-color-white-trans-400:hsla(0,0%,100%,.4);--mico-color-white-trans-500:hsla(0,0%,100%,.5);--mico-color-white-trans-600:hsla(0,0%,100%,.6);--mico-color-white-trans-700:hsla(0,0%,100%,.7);--mico-color-white-trans-800:hsla(0,0%,100%,.8);--mico-color-white-trans-900:hsla(0,0%,100%,.9);--mico-color-text-primary:#111827;--mico-color-text-secondary:#6b7280;--mico-color-text-muted:#9ca3af;--mico-color-text-inverse:#fff;--mico-color-bg-primary:#fff;--mico-color-bg-secondary:#f9fafb;--mico-color-bg-muted:#f3f4f6;--mico-color-bg-inverse:#111827;--mico-color-border-primary:#e5e7eb;--mico-color-border-secondary:#d1d5db;--mico-color-border-muted:#f3f4f6;--mico-color-border-focus:#3b82f6;--mico-color-red-100:#fee2e2;--mico-color-red-200:#fecaca;--mico-color-red-300:#fca5a5;--mico-color-red-400:#f87171;--mico-color-red-500:#ef4444;--mico-color-red-600:#dc2626;--mico-color-red-700:#b91c1c;--mico-color-red-800:#991b1b;--mico-color-red-900:#7f1d1d;--mico-color-yellow-100:#fef3c7;--mico-color-yellow-200:#fde68a;--mico-color-yellow-300:#fcd34d;--mico-color-yellow-400:#fbbf24;--mico-color-yellow-500:#f59e0b;--mico-color-yellow-600:#d97706;--mico-color-yellow-700:#b45309;--mico-color-yellow-800:#92400e;--mico-color-yellow-900:#78350f;--mico-color-green-100:#d1fae5;--mico-color-green-200:#a7f3d0;--mico-color-green-300:#6ee7b7;--mico-color-green-400:#34d399;--mico-color-green-500:#10b981;--mico-color-green-600:#059669;--mico-color-green-700:#047857;--mico-color-green-800:#065f46;--mico-color-green-900:#064e3b;--mico-color-blue-100:#dbeafe;--mico-color-blue-200:#bfdbfe;--mico-color-blue-300:#93c5fd;--mico-color-blue-400:#60a5fa;--mico-color-blue-500:#3b82f6;--mico-color-blue-600:#2563eb;--mico-color-blue-700:#1d4ed8;--mico-color-blue-800:#1e40af;--mico-color-blue-900:#1e3a8a;--mico-color-indigo-100:#e0e7ff;--mico-color-indigo-200:#c7d2fe;--mico-color-indigo-300:#a5b4fc;--mico-color-indigo-400:#818cf8;--mico-color-indigo-500:#6366f1;--mico-color-indigo-600:#4f46e5;--mico-color-indigo-700:#4338ca;--mico-color-indigo-800:#3730a3;--mico-color-indigo-900:#312e81;--mico-color-purple-100:#ede9fe;--mico-color-purple-200:#ddd6fe;--mico-color-purple-300:#c4b5fd;--mico-color-purple-400:#a78bfa;--mico-color-purple-500:#8b5cf6;--mico-color-purple-600:#7c3aed;--mico-color-purple-700:#6d28d9;--mico-color-purple-800:#5b21b6;--mico-color-purple-900:#4c1d95;--mico-color-pink-100:#fce7f3;--mico-color-pink-200:#fbcfe8;--mico-color-pink-300:#f9a8d4;--mico-color-pink-400:#f472b6;--mico-color-pink-500:#ec4899;--mico-color-pink-600:#db2777;--mico-color-pink-700:#be185d;--mico-color-pink-800:#9d174d;--mico-color-pink-900:#831843}@media (prefers-color-scheme:dark){:root{--mico-shadow-sm:var(--mico-shadow-sm-dark);--mico-shadow-md:var(--mico-shadow-md-dark);--mico-shadow-lg:var(--mico-shadow-lg-dark);--mico-shadow-xl:var(--mico-shadow-xl-dark);--mico-shadow-inset-sm:inset 0 1px 2px hsla(0,0%,100%,.05);--mico-shadow-inset-md:inset 0 4px 6px hsla(0,0%,100%,.1);--mico-shadow-focus:0 0 0 3px rgba(191,219,254,.6)}}@media (prefers-contrast:high){:root{--mico-shadow-focus:0 0 0 4px #000;--mico-shadow-sm:0 0 0 1px currentColor;--mico-shadow-md:0 0 0 2px currentColor;--mico-shadow-lg:0 0 0 3px currentColor;--mico-shadow-xl:0 0 0 4px currentColor}}