/**
 * Mico CSS Framework - Interactive States
 *
 * This file defines utility classes for interactive states (hover, focus, active).
 * These classes follow a consistent naming pattern for easy use and extension.
 *
 * NAMING CONVENTION:
 * - Hover: .hover-{property}-{value}
 * - Focus: .focus-{property}-{value}
 * - Active: .active-{property}-{value}
 *
 * USAGE EXAMPLES:
 * <button class="hover-bg-primary">Hover for primary background</button>
 * <a class="hover-text-accent focus-text-primary">Interactive link</a>
 */

/* ========================================================================== */
/* BASE STATE UTILITIES                                                       */
/* ========================================================================== */

/**
 * Base transition for all interactive state classes
 * This ensures smooth transitions when states change
 */
[class^="hover-"], [class*=" hover-"],
[class^="focus-"], [class*=" focus-"],
[class^="active-"], [class*=" active-"] {
    transition: var(--mico-transition-all);
}

/* ========================================================================== */
/* HOVER STATE UTILITIES                                                      */
/* ========================================================================== */

/**
 * BACKGROUND COLOR HOVER STATES
 *
 * These utilities apply background colors on hover
 */

/* ---------- Brand Colors ---------- */
.hover-bg-primary:hover { background-color: var(--mico-color-primary) !important; }
.hover-bg-secondary:hover { background-color: var(--mico-color-secondary) !important; }
.hover-bg-accent:hover { background-color: var(--mico-color-accent) !important; }

/* ---------- Primary Color Variations ---------- */
/* Tints - Lighter variations */
.hover-bg-primary-light:hover { background-color: var(--mico-color-primary-light) !important; }
.hover-bg-primary-2xlight:hover { background-color: var(--mico-color-primary-2xlight) !important; }
.hover-bg-primary-3xlight:hover { background-color: var(--mico-color-primary-3xlight) !important; }
.hover-bg-primary-4xlight:hover { background-color: var(--mico-color-primary-4xlight) !important; }
.hover-bg-primary-5xlight:hover { background-color: var(--mico-color-primary-5xlight) !important; }

/* Shades - Darker variations */
.hover-bg-primary-dark:hover { background-color: var(--mico-color-primary-dark) !important; }
.hover-bg-primary-2xdark:hover { background-color: var(--mico-color-primary-2xdark) !important; }
.hover-bg-primary-3xdark:hover { background-color: var(--mico-color-primary-3xdark) !important; }
.hover-bg-primary-4xdark:hover { background-color: var(--mico-color-primary-4xdark) !important; }
.hover-bg-primary-5xdark:hover { background-color: var(--mico-color-primary-5xdark) !important; }


/* ---------- Secondary Color Variations ---------- */
/* Tints - Lighter variations */
.hover-bg-secondary-light:hover { background-color: var(--mico-color-secondary-light) !important; }
.hover-bg-secondary-2xlight:hover { background-color: var(--mico-color-secondary-2xlight) !important; }
.hover-bg-secondary-3xlight:hover { background-color: var(--mico-color-secondary-3xlight) !important; }
.hover-bg-secondary-4xlight:hover { background-color: var(--mico-color-secondary-4xlight) !important; }
.hover-bg-secondary-5xlight:hover { background-color: var(--mico-color-secondary-5xlight) !important; }

/* Shades - Darker variations */
.hover-bg-secondary-dark:hover { background-color: var(--mico-color-secondary-dark) !important; }
.hover-bg-secondary-2xdark:hover { background-color: var(--mico-color-secondary-2xdark) !important; }
.hover-bg-secondary-3xdark:hover { background-color: var(--mico-color-secondary-3xdark) !important; }
.hover-bg-secondary-4xdark:hover { background-color: var(--mico-color-secondary-4xdark) !important; }
.hover-bg-secondary-5xdark:hover { background-color: var(--mico-color-secondary-5xdark) !important; }



/* ---------- Accent Color Variations ---------- */
/* Tints - Lighter variations */
.hover-bg-accent-light:hover { background-color: var(--mico-color-accent-light) !important; }
.hover-bg-accent-2xlight:hover { background-color: var(--mico-color-accent-2xlight) !important; }
.hover-bg-accent-3xlight:hover { background-color: var(--mico-color-accent-3xlight) !important; }
.hover-bg-accent-4xlight:hover { background-color: var(--mico-color-accent-4xlight) !important; }
.hover-bg-accent-5xlight:hover { background-color: var(--mico-color-accent-5xlight) !important; }

/* Shades - Darker variations */
.hover-bg-accent-dark:hover { background-color: var(--mico-color-accent-dark) !important; }
.hover-bg-accent-2xdark:hover { background-color: var(--mico-color-accent-2xdark) !important; }
.hover-bg-accent-3xdark:hover { background-color: var(--mico-color-accent-3xdark) !important; }
.hover-bg-accent-4xdark:hover { background-color: var(--mico-color-accent-4xdark) !important; }
.hover-bg-accent-5xdark:hover { background-color: var(--mico-color-accent-5xdark) !important; }

/* ---------- Grayscale ---------- */
.hover-bg-gray-100:hover { background-color: var(--mico-color-gray-100) !important; }
.hover-bg-gray-200:hover { background-color: var(--mico-color-gray-200) !important; }
.hover-bg-gray-300:hover { background-color: var(--mico-color-gray-300) !important; }
.hover-bg-gray-400:hover { background-color: var(--mico-color-gray-400) !important; }
.hover-bg-gray-500:hover { background-color: var(--mico-color-gray-500) !important; }
.hover-bg-gray-600:hover { background-color: var(--mico-color-gray-600) !important; }
.hover-bg-gray-700:hover { background-color: var(--mico-color-gray-700) !important; }
.hover-bg-gray-800:hover { background-color: var(--mico-color-gray-800) !important; }
.hover-bg-gray-900:hover { background-color: var(--mico-color-gray-900) !important; }

/* ---------- Black Scale ---------- */
.hover-bg-black-100:hover { background-color: var(--mico-color-black-100) !important; }
.hover-bg-black-200:hover { background-color: var(--mico-color-black-200) !important; }
.hover-bg-black-300:hover { background-color: var(--mico-color-black-300) !important; }
.hover-bg-black-400:hover { background-color: var(--mico-color-black-400) !important; }
.hover-bg-black-500:hover { background-color: var(--mico-color-black-500) !important; }
.hover-bg-black-600:hover { background-color: var(--mico-color-black-600) !important; }
.hover-bg-black-700:hover { background-color: var(--mico-color-black-700) !important; }
.hover-bg-black-800:hover { background-color: var(--mico-color-black-800) !important; }
.hover-bg-black-900:hover { background-color: var(--mico-color-black-900) !important; }

/* ---------- Transparency ---------- */
.hover-bg-black-trans-100:hover { background-color: var(--mico-color-black-trans-100) !important; }
.hover-bg-black-trans-200:hover { background-color: var(--mico-color-black-trans-200) !important; }
.hover-bg-black-trans-300:hover { background-color: var(--mico-color-black-trans-300) !important; }
.hover-bg-black-trans-400:hover { background-color: var(--mico-color-black-trans-400) !important; }
.hover-bg-black-trans-500:hover { background-color: var(--mico-color-black-trans-500) !important; }
.hover-bg-black-trans-600:hover { background-color: var(--mico-color-black-trans-600) !important; }
.hover-bg-black-trans-700:hover { background-color: var(--mico-color-black-trans-700) !important; }
.hover-bg-black-trans-800:hover { background-color: var(--mico-color-black-trans-800) !important; }
.hover-bg-black-trans-900:hover { background-color: var(--mico-color-black-trans-900) !important; }

/* ---------- State Colors ---------- */
.hover-bg-success:hover { background-color: var(--mico-color-success) !important; }
.hover-bg-warning:hover { background-color: var(--mico-color-warning) !important; }
.hover-bg-error:hover { background-color: var(--mico-color-error) !important; }
.hover-bg-info:hover { background-color: var(--mico-color-info) !important; }

/**
 * TEXT COLOR HOVER STATES
 *
 * These utilities apply text colors on hover
 */
.hover-text-primary:hover { color: var(--mico-color-primary) !important; }
.hover-text-secondary:hover { color: var(--mico-color-secondary) !important; }
.hover-text-accent:hover { color: var(--mico-color-accent) !important; }


/* ========================================================================== */
/* FOCUS STATE UTILITIES                                                      */
/* ========================================================================== */

/**
 * BACKGROUND COLOR FOCUS STATES
 *
 * These utilities apply background colors on focus
 */

/* ---------- Brand Colors ---------- */
.focus-bg-primary:focus { background-color: var(--mico-color-primary) !important; }
.focus-bg-secondary:focus { background-color: var(--mico-color-secondary) !important; }
.focus-bg-accent:focus { background-color: var(--mico-color-accent) !important; }

/* ---------- Primary Color Variations ---------- */
/* Tints - Lighter variations */
.focus-bg-primary-light:focus { background-color: var(--mico-color-primary-light) !important; }
.focus-bg-primary-2xlight:focus { background-color: var(--mico-color-primary-2xlight) !important; }
.focus-bg-primary-3xlight:focus { background-color: var(--mico-color-primary-3xlight) !important; }
.focus-bg-primary-4xlight:focus { background-color: var(--mico-color-primary-4xlight) !important; }
.focus-bg-primary-5xlight:focus { background-color: var(--mico-color-primary-5xlight) !important; }

/* Shades - Darker variations */
.focus-bg-primary-dark:focus { background-color: var(--mico-color-primary-dark) !important; }
.focus-bg-primary-2xdark:focus { background-color: var(--mico-color-primary-2xdark) !important; }
.focus-bg-primary-3xdark:focus { background-color: var(--mico-color-primary-3xdark) !important; }
.focus-bg-primary-4xdark:focus { background-color: var(--mico-color-primary-4xdark) !important; }
.focus-bg-primary-5xdark:focus { background-color: var(--mico-color-primary-5xdark) !important; }

/* ---------- Secondary Color Variations ---------- */
/* Tints - Lighter variations */
.focus-bg-secondary-light:focus { background-color: var(--mico-color-secondary-light) !important; }
.focus-bg-secondary-2xlight:focus { background-color: var(--mico-color-secondary-2xlight) !important; }
.focus-bg-secondary-3xlight:focus { background-color: var(--mico-color-secondary-3xlight) !important; }
.focus-bg-secondary-4xlight:focus { background-color: var(--mico-color-secondary-4xlight) !important; }
.focus-bg-secondary-5xlight:focus { background-color: var(--mico-color-secondary-5xlight) !important; }

/* Shades - Darker variations */
.focus-bg-secondary-dark:focus { background-color: var(--mico-color-secondary-dark) !important; }
.focus-bg-secondary-2xdark:focus { background-color: var(--mico-color-secondary-2xdark) !important; }
.focus-bg-secondary-3xdark:focus { background-color: var(--mico-color-secondary-3xdark) !important; }
.focus-bg-secondary-4xdark:focus { background-color: var(--mico-color-secondary-4xdark) !important; }
.focus-bg-secondary-5xdark:focus { background-color: var(--mico-color-secondary-5xdark) !important; }


/* ---------- Accent Color Variations ---------- */
/* Tints - Lighter variations */
.focus-bg-accent-light:focus { background-color: var(--mico-color-accent-light) !important; }
.focus-bg-accent-2xlight:focus { background-color: var(--mico-color-accent-2xlight) !important; }
.focus-bg-accent-3xlight:focus { background-color: var(--mico-color-accent-3xlight) !important; }
.focus-bg-accent-4xlight:focus { background-color: var(--mico-color-accent-4xlight) !important; }
.focus-bg-accent-5xlight:focus { background-color: var(--mico-color-accent-5xlight) !important; }

/* Shades - Darker variations */
.focus-bg-accent-dark:focus { background-color: var(--mico-color-accent-dark) !important; }
.focus-bg-accent-2xdark:focus { background-color: var(--mico-color-accent-2xdark) !important; }
.focus-bg-accent-3xdark:focus { background-color: var(--mico-color-accent-3xdark) !important; }
.focus-bg-accent-4xdark:focus { background-color: var(--mico-color-accent-4xdark) !important; }
.focus-bg-accent-5xdark:focus { background-color: var(--mico-color-accent-5xdark) !important; }


/* ---------- Grayscale ---------- */
.focus-bg-gray-100:focus { background-color: var(--mico-color-gray-100) !important; }
.focus-bg-gray-200:focus { background-color: var(--mico-color-gray-200) !important; }
.focus-bg-gray-300:focus { background-color: var(--mico-color-gray-300) !important; }
.focus-bg-gray-400:focus { background-color: var(--mico-color-gray-400) !important; }
.focus-bg-gray-500:focus { background-color: var(--mico-color-gray-500) !important; }
.focus-bg-gray-600:focus { background-color: var(--mico-color-gray-600) !important; }
.focus-bg-gray-700:focus { background-color: var(--mico-color-gray-700) !important; }
.focus-bg-gray-800:focus { background-color: var(--mico-color-gray-800) !important; }
.focus-bg-gray-900:focus { background-color: var(--mico-color-gray-900) !important; }

/* ---------- Black Scale ---------- */
.focus-bg-black-100:focus { background-color: var(--mico-color-black-100) !important; }
.focus-bg-black-200:focus { background-color: var(--mico-color-black-200) !important; }
.focus-bg-black-300:focus { background-color: var(--mico-color-black-300) !important; }
.focus-bg-black-400:focus { background-color: var(--mico-color-black-400) !important; }
.focus-bg-black-500:focus { background-color: var(--mico-color-black-500) !important; }
.focus-bg-black-600:focus { background-color: var(--mico-color-black-600) !important; }
.focus-bg-black-700:focus { background-color: var(--mico-color-black-700) !important; }
.focus-bg-black-800:focus { background-color: var(--mico-color-black-800) !important; }
.focus-bg-black-900:focus { background-color: var(--mico-color-black-900) !important; }

/* ---------- Transparency ---------- */
.focus-bg-black-trans-100:focus { background-color: var(--mico-color-black-trans-100) !important; }
.focus-bg-black-trans-200:focus { background-color: var(--mico-color-black-trans-200) !important; }
.focus-bg-black-trans-300:focus { background-color: var(--mico-color-black-trans-300) !important; }
.focus-bg-black-trans-400:focus { background-color: var(--mico-color-black-trans-400) !important; }
.focus-bg-black-trans-500:focus { background-color: var(--mico-color-black-trans-500) !important; }
.focus-bg-black-trans-600:focus { background-color: var(--mico-color-black-trans-600) !important; }
.focus-bg-black-trans-700:focus { background-color: var(--mico-color-black-trans-700) !important; }
.focus-bg-black-trans-800:focus { background-color: var(--mico-color-black-trans-800) !important; }
.focus-bg-black-trans-900:focus { background-color: var(--mico-color-black-trans-900) !important; }

/* ---------- State Colors ---------- */
.focus-bg-success:focus { background-color: var(--mico-color-success) !important; }
.focus-bg-warning:focus { background-color: var(--mico-color-warning) !important; }
.focus-bg-error:focus { background-color: var(--mico-color-error) !important; }
.focus-bg-info:focus { background-color: var(--mico-color-info) !important; }

/**
 * TEXT COLOR FOCUS STATES
 *
 * These utilities apply text colors on focus
 */
.focus-text-primary:focus { color: var(--mico-color-primary) !important; }
.focus-text-secondary:focus { color: var(--mico-color-secondary) !important; }
.focus-text-accent:focus { color: var(--mico-color-accent) !important; }

/* ========================================================================== */
/* ACTIVE STATE UTILITIES                                                     */
/* ========================================================================== */

/**
 * BACKGROUND COLOR ACTIVE STATES
 *
 * These utilities apply background colors on active (pressed) state
 */

/* ---------- Brand Colors ---------- */
.active-bg-primary:active { background-color: var(--mico-color-primary) !important; }
.active-bg-secondary:active { background-color: var(--mico-color-secondary) !important; }
.active-bg-accent:active { background-color: var(--mico-color-accent) !important; }

/* ---------- Primary Color Variations ---------- */
/* Tints - Lighter variations */
.active-bg-primary-light:active { background-color: var(--mico-color-primary-light) !important; }
.active-bg-primary-2xlight:active { background-color: var(--mico-color-primary-2xlight) !important; }
.active-bg-primary-3xlight:active { background-color: var(--mico-color-primary-3xlight) !important; }
.active-bg-primary-4xlight:active { background-color: var(--mico-color-primary-4xlight) !important; }
.active-bg-primary-5xlight:active { background-color: var(--mico-color-primary-5xlight) !important; }

/* Shades - Darker variations */
.active-bg-primary-dark:active { background-color: var(--mico-color-primary-dark) !important; }
.active-bg-primary-2xdark:active { background-color: var(--mico-color-primary-2xdark) !important; }
.active-bg-primary-3xdark:active { background-color: var(--mico-color-primary-3xdark) !important; }
.active-bg-primary-4xdark:active { background-color: var(--mico-color-primary-4xdark) !important; }
.active-bg-primary-5xdark:active { background-color: var(--mico-color-primary-5xdark) !important; }

/* ---------- Secondary Color Variations ---------- */
/* Tints - Lighter variations */
.active-bg-secondary-light:active { background-color: var(--mico-color-secondary-light) !important; }
.active-bg-secondary-2xlight:active { background-color: var(--mico-color-secondary-2xlight) !important; }
.active-bg-secondary-3xlight:active { background-color: var(--mico-color-secondary-3xlight) !important; }
.active-bg-secondary-4xlight:active { background-color: var(--mico-color-secondary-4xlight) !important; }
.active-bg-secondary-5xlight:active { background-color: var(--mico-color-secondary-5xlight) !important; }

/* Shades - Darker variations */
.active-bg-secondary-dark:active { background-color: var(--mico-color-secondary-dark) !important; }
.active-bg-secondary-2xdark:active { background-color: var(--mico-color-secondary-2xdark) !important; }
.active-bg-secondary-3xdark:active { background-color: var(--mico-color-secondary-3xdark) !important; }
.active-bg-secondary-4xdark:active { background-color: var(--mico-color-secondary-4xdark) !important; }
.active-bg-secondary-5xdark:active { background-color: var(--mico-color-secondary-5xdark) !important; }

/* ---------- Accent Color Variations ---------- */
/* Tints - Lighter variations */
.active-bg-accent-light:active { background-color: var(--mico-color-accent-light) !important; }
.active-bg-accent-2xlight:active { background-color: var(--mico-color-accent-2xlight) !important; }
.active-bg-accent-3xlight:active { background-color: var(--mico-color-accent-3xlight) !important; }
.active-bg-accent-4xlight:active { background-color: var(--mico-color-accent-4xlight) !important; }
.active-bg-accent-5xlight:active { background-color: var(--mico-color-accent-5xlight) !important; }

/* Shades - Darker variations */
.active-bg-accent-dark:active { background-color: var(--mico-color-accent-dark) !important; }
.active-bg-accent-2xdark:active { background-color: var(--mico-color-accent-2xdark) !important; }
.active-bg-accent-3xdark:active { background-color: var(--mico-color-accent-3xdark) !important; }
.active-bg-accent-4xdark:active { background-color: var(--mico-color-accent-4xdark) !important; }
.active-bg-accent-5xdark:active { background-color: var(--mico-color-accent-5xdark) !important; }

/* ========================================================================== */
/* BORDER STATE UTILITIES                                                    */
/* ========================================================================== */

/**
 * Border Color States
 *
 * These utilities change border colors on hover, focus, and active states.
 */

/* Hover Border Colors */
.hover-border-primary:hover { border-color: var(--mico-color-primary) !important; }
.hover-border-secondary:hover { border-color: var(--mico-color-secondary) !important; }
.hover-border-accent:hover { border-color: var(--mico-color-accent) !important; }
.hover-border-success:hover { border-color: var(--mico-color-success) !important; }
.hover-border-error:hover { border-color: var(--mico-color-error) !important; }
.hover-border-warning:hover { border-color: var(--mico-color-warning) !important; }
.hover-border-info:hover { border-color: var(--mico-color-info) !important; }
.hover-border-gray-300:hover { border-color: var(--mico-color-gray-300) !important; }
.hover-border-gray-500:hover { border-color: var(--mico-color-gray-500) !important; }
.hover-border-gray-700:hover { border-color: var(--mico-color-gray-700) !important; }

/* Focus Border Colors */
.focus-border-primary:focus { border-color: var(--mico-color-primary) !important; }
.focus-border-secondary:focus { border-color: var(--mico-color-secondary) !important; }
.focus-border-accent:focus { border-color: var(--mico-color-accent) !important; }
.focus-border-success:focus { border-color: var(--mico-color-success) !important; }
.focus-border-error:focus { border-color: var(--mico-color-error) !important; }
.focus-border-warning:focus { border-color: var(--mico-color-warning) !important; }
.focus-border-info:focus { border-color: var(--mico-color-info) !important; }

/* Active Border Colors */
.active-border-primary:active { border-color: var(--mico-color-primary-dark) !important; }
.active-border-secondary:active { border-color: var(--mico-color-secondary-dark) !important; }
.active-border-accent:active { border-color: var(--mico-color-accent-dark) !important; }

/* ========================================================================== */
/* SHADOW STATE UTILITIES                                                    */
/* ========================================================================== */

/**
 * Shadow States
 *
 * These utilities change box shadows on hover, focus, and active states.
 */

/* Hover Shadows */
.hover-shadow:hover { box-shadow: var(--mico-shadow-md) !important; }
.hover-shadow-sm:hover { box-shadow: var(--mico-shadow-sm) !important; }
.hover-shadow-lg:hover { box-shadow: var(--mico-shadow-lg) !important; }
.hover-shadow-xl:hover { box-shadow: var(--mico-shadow-xl) !important; }
.hover-shadow-2xl:hover { box-shadow: var(--mico-shadow-2xl) !important; }
.hover-shadow-none:hover { box-shadow: var(--mico-shadow-none) !important; }

/* Focus Shadows */
.focus-shadow:focus { box-shadow: var(--mico-shadow-md) !important; }
.focus-shadow-lg:focus { box-shadow: var(--mico-shadow-lg) !important; }

/* Active Shadows */
.active-shadow-sm:active { box-shadow: var(--mico-shadow-sm) !important; }
.active-shadow-none:active { box-shadow: var(--mico-shadow-none) !important; }

/* ========================================================================== */
/* TRANSFORM STATE UTILITIES                                                 */
/* ========================================================================== */

/**
 * Transform States
 *
 * These utilities apply transforms on hover, focus, and active states.
 */

/* Hover Transforms */
.hover-scale-105:hover { transform: scale(1.05) !important; }
.hover-scale-110:hover { transform: scale(1.1) !important; }
.hover-scale-95:hover { transform: scale(0.95) !important; }
.hover-translate-y-1:hover { transform: translateY(-0.25rem) !important; }
.hover-translate-y-2:hover { transform: translateY(-0.5rem) !important; }
.hover-rotate-1:hover { transform: rotate(1deg) !important; }
.hover-rotate-3:hover { transform: rotate(3deg) !important; }
.hover-rotate-6:hover { transform: rotate(6deg) !important; }

/* Focus Transforms */
.focus-scale-105:focus { transform: scale(1.05) !important; }
.focus-translate-y-1:focus { transform: translateY(-0.25rem) !important; }

/* Active Transforms */
.active-scale-95:active { transform: scale(0.95) !important; }
.active-translate-y-0:active { transform: translateY(0) !important; }

/* ========================================================================== */
/* OPACITY STATE UTILITIES                                                   */
/* ========================================================================== */

/**
 * Opacity States
 *
 * These utilities change opacity on hover, focus, and active states.
 */

/* Hover Opacity */
.hover-opacity-100:hover { opacity: var(--mico-opacity-100) !important; }
.hover-opacity-75:hover { opacity: var(--mico-opacity-75) !important; }
.hover-opacity-50:hover { opacity: var(--mico-opacity-50) !important; }
.hover-opacity-25:hover { opacity: var(--mico-opacity-25) !important; }
.hover-opacity-0:hover { opacity: var(--mico-opacity-0) !important; }

/* Focus Opacity */
.focus-opacity-100:focus { opacity: var(--mico-opacity-100) !important; }
.focus-opacity-75:focus { opacity: var(--mico-opacity-75) !important; }

/* Active Opacity */
.active-opacity-75:active { opacity: var(--mico-opacity-75) !important; }
.active-opacity-50:active { opacity: var(--mico-opacity-50) !important; }

/* ========================================================================== */
/* RING STATE UTILITIES                                                      */
/* ========================================================================== */

/**
 * Ring States
 *
 * These utilities apply ring effects on hover, focus, and active states.
 */

/* Hover Rings */
.hover-ring:hover { box-shadow: 0 0 0 var(--mico-ring-width-2) var(--mico-color-primary) !important; }
.hover-ring-primary:hover { box-shadow: 0 0 0 var(--mico-ring-width-2) var(--mico-color-primary) !important; }
.hover-ring-secondary:hover { box-shadow: 0 0 0 var(--mico-ring-width-2) var(--mico-color-secondary) !important; }
.hover-ring-accent:hover { box-shadow: 0 0 0 var(--mico-ring-width-2) var(--mico-color-accent) !important; }

/* Focus Rings */
.focus-ring:focus { box-shadow: 0 0 0 var(--mico-ring-width-2) var(--mico-color-primary) !important; }
.focus-ring-primary:focus { box-shadow: 0 0 0 var(--mico-ring-width-2) var(--mico-color-primary) !important; }
.focus-ring-secondary:focus { box-shadow: 0 0 0 var(--mico-ring-width-2) var(--mico-color-secondary) !important; }
.focus-ring-accent:focus { box-shadow: 0 0 0 var(--mico-ring-width-2) var(--mico-color-accent) !important; }
.focus-ring-error:focus { box-shadow: 0 0 0 var(--mico-ring-width-2) var(--mico-color-error) !important; }

/* Ring with Offset */
.focus-ring-offset-2:focus {
  box-shadow: 0 0 0 var(--mico-ring-offset-width-2) var(--mico-color-white),
              0 0 0 calc(var(--mico-ring-width-2) + var(--mico-ring-offset-width-2)) var(--mico-color-primary) !important;
}

/* ---------- Grayscale ---------- */
.active-bg-gray-100:active { background-color: var(--mico-color-gray-100) !important; }
.active-bg-gray-200:active { background-color: var(--mico-color-gray-200) !important; }
.active-bg-gray-300:active { background-color: var(--mico-color-gray-300) !important; }
.active-bg-gray-400:active { background-color: var(--mico-color-gray-400) !important; }
.active-bg-gray-500:active { background-color: var(--mico-color-gray-500) !important; }
.active-bg-gray-600:active { background-color: var(--mico-color-gray-600) !important; }
.active-bg-gray-700:active { background-color: var(--mico-color-gray-700) !important; }
.active-bg-gray-800:active { background-color: var(--mico-color-gray-800) !important; }
.active-bg-gray-900:active { background-color: var(--mico-color-gray-900) !important; }

/* ---------- Black Scale ---------- */
.active-bg-black-100:active { background-color: var(--mico-color-black-100) !important; }
.active-bg-black-200:active { background-color: var(--mico-color-black-200) !important; }
.active-bg-black-300:active { background-color: var(--mico-color-black-300) !important; }
.active-bg-black-400:active { background-color: var(--mico-color-black-400) !important; }
.active-bg-black-500:active { background-color: var(--mico-color-black-500) !important; }
.active-bg-black-600:active { background-color: var(--mico-color-black-600) !important; }
.active-bg-black-700:active { background-color: var(--mico-color-black-700) !important; }
.active-bg-black-800:active { background-color: var(--mico-color-black-800) !important; }
.active-bg-black-900:active { background-color: var(--mico-color-black-900) !important; }

/* ---------- Transparency ---------- */
.active-bg-black-trans-100:active { background-color: var(--mico-color-black-trans-100) !important; }
.active-bg-black-trans-200:active { background-color: var(--mico-color-black-trans-200) !important; }
.active-bg-black-trans-300:active { background-color: var(--mico-color-black-trans-300) !important; }
.active-bg-black-trans-400:active { background-color: var(--mico-color-black-trans-400) !important; }
.active-bg-black-trans-500:active { background-color: var(--mico-color-black-trans-500) !important; }
.active-bg-black-trans-600:active { background-color: var(--mico-color-black-trans-600) !important; }
.active-bg-black-trans-700:active { background-color: var(--mico-color-black-trans-700) !important; }
.active-bg-black-trans-800:active { background-color: var(--mico-color-black-trans-800) !important; }
.active-bg-black-trans-900:active { background-color: var(--mico-color-black-trans-900) !important; }

/* ---------- State Colors ---------- */
.active-bg-success:active { background-color: var(--mico-color-success) !important; }
.active-bg-warning:active { background-color: var(--mico-color-warning) !important; }
.active-bg-error:active { background-color: var(--mico-color-error) !important; }
.active-bg-info:active { background-color: var(--mico-color-info) !important; }

/**
 * TEXT COLOR ACTIVE STATES
 *
 * These utilities apply text colors on active state
 */
.active-text-primary:active { color: var(--mico-color-primary) !important; }
.active-text-secondary:active { color: var(--mico-color-secondary) !important; }
.active-text-accent:active { color: var(--mico-color-accent) !important; }

/* ========================================================================== */
/* ADDITIONAL INTERACTIVE UTILITIES                                           */
/* ========================================================================== */

/**
 * TRANSFORM HOVER EFFECTS
 *
 * These utilities apply transform effects on hover for enhanced interactivity
 */
.hover-scale:hover { transform: scale(1.05) !important; }
.hover-scale-lg:hover { transform: scale(1.1) !important; }
.hover-scale-sm:hover { transform: scale(1.025) !important; }
.hover-rotate-1:hover { transform: rotate(1deg) !important; }
.hover-rotate-2:hover { transform: rotate(2deg) !important; }
.hover-rotate-5:hover { transform: rotate(5deg) !important; }
.hover-translate-up-1:hover { transform: translateY(-1px) !important; }
.hover-translate-up-2:hover { transform: translateY(-2px) !important; }
.hover-translate-up-4:hover { transform: translateY(-4px) !important; }

/**
 * SHADOW HOVER EFFECTS
 *
 * These utilities apply shadow effects on hover
 */
.hover-shadow:hover { box-shadow: var(--mico-shadow-md) !important; }
.hover-shadow-lg:hover { box-shadow: var(--mico-shadow-lg) !important; }
.hover-shadow-sm:hover { box-shadow: var(--mico-shadow-sm) !important; }

/**
 * OPACITY HOVER EFFECTS
 *
 * These utilities apply opacity changes on hover
 */
.hover-opacity-75:hover { opacity: 0.75 !important; }
.hover-opacity-50:hover { opacity: 0.5 !important; }
.hover-opacity-100:hover { opacity: 1 !important; }

/**
 * FILTER HOVER EFFECTS
 *
 * These utilities apply filter effects on hover
 */
.hover-blur:hover { filter: blur(4px) !important; }
.hover-brightness-110:hover { filter: brightness(1.1) !important; }
.hover-brightness-125:hover { filter: brightness(1.25) !important; }
.hover-grayscale:hover { filter: grayscale(100%) !important; }
.hover-sepia:hover { filter: sepia(100%) !important; }

/* Complete hover effect for opacity, transforms*/

/* ========================================================================== */
/* USAGE EXAMPLES AND DOCUMENTATION                                           */
/* ========================================================================== */

/**
 * HOW TO USE INTERACTIVE STATE UTILITIES
 *
 * These utilities can be combined to create rich interactive experiences.
 * Here are some common patterns:
 *
 * 1. Basic hover effect for buttons:
 *    <button class="hover-bg-primary hover-text-white">Button</button>
 *
 * 2. Elevated card on hover:
 *    <div class="hover-elevate hover-bg-primary">Card content</div>
 *
 * 3. Interactive link:
 *    <a class="hover-text-primary focus-text-primary active-text-primary">Link</a>
 *
 * 4. Subtle image hover effect:
 *    <img class="hover-brightness-110 hover-scale-sm" src="image.jpg" alt="Image">
 *
 * 5. Button with multiple states:
 *    <button class="hover-bg-primary focus-bg-primary active-bg-primary">Button</button>
 */

